#ifndef __PID_APP_H__
#define __PID_APP_H__

#ifdef __cplusplus
extern "C" {
#endif

#include "bsp_system.h"
#include "motor_app.h"
#include "gray_app.h"

// PID参数结构体
typedef struct
{
    float kp;          // 比例系数
    float ki;          // 积分系数
    float kd;          // 微分系数
    float out_min;     // 输出最小值
    float out_max;     // 输出最大值
} PidParams_t;

/* 导出变量 */
extern PID_T pid_speed_left;  // 左轮速度环
extern PID_T pid_speed_right; // 右轮速度环
extern PID_T pid_line;        // 循迹环
extern bool pid_running;      // PID控制使能开关
extern int basic_speed;       // 基础速度
extern uint8_t pid_mode;      // PID模式

extern PidParams_t pid_params_left;
extern PidParams_t pid_params_right;
extern PidParams_t pid_params_line;

/* 导出函数 */
void PID_Init(void);
void PID_Task(void);
void pid_line_control(void);

#ifdef __cplusplus
}
#endif

#endif
