Component: Arm Compiler for Embedded 6.21 Tool: armlink [5ec1fa00]

==============================================================================

Section Cross References

    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    startup_mspm0g350x_uvision.o(RESET) refers to startup_mspm0g350x_uvision.o(.text) for Reset_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to scheduler.o(.text.SysTick_Handler) for SysTick_Handler
    startup_mspm0g350x_uvision.o(RESET) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for GROUP1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART3_IRQHandler) for UART3_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART1_IRQHandler) for UART1_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART2_IRQHandler) for UART2_IRQHandler
    startup_mspm0g350x_uvision.o(RESET) refers to uart_driver.o(.text.UART0_IRQHandler) for UART0_IRQHandler
    startup_mspm0g350x_uvision.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for SYSCFG_DL_initPower
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for SYSCFG_DL_GPIO_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for SYSCFG_DL_SYSCTL_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) for SYSCFG_DL_MOTOR_PWM_LEFT_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) for SYSCFG_DL_MOTOR_PWM_RIGHT_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) for SYSCFG_DL_IMU_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) for SYSCFG_DL_GRAY_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for SYSCFG_DL_UART_0_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for SYSCFG_DL_UART_1_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for SYSCFG_DL_UART_2_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) for SYSCFG_DL_UART_3_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for SYSCFG_DL_SYSTICK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for SYSCFG_DL_SYSCTL_CLK_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup) for gMOTOR_PWM_RIGHTBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.bss.gUART_3Backup) for gUART_3Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for DL_GPIO_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for DL_Timer_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for DL_I2C_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for DL_UART_reset
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for DL_GPIO_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for DL_Timer_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for DL_I2C_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for DL_UART_enablePower
    ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) refers to dl_common.o(.text.DL_Common_delayCycles) for DL_Common_delayCycles
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_initPower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for DL_GPIO_initPeripheralAnalogFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for DL_GPIO_initPeripheralOutputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for DL_GPIO_enableOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for DL_GPIO_initPeripheralInputFunctionFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for DL_GPIO_enableHiZ
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for DL_GPIO_initPeripheralInputFunction
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for DL_GPIO_initDigitalOutputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for DL_GPIO_initDigitalInputFeatures
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for DL_GPIO_initDigitalOutput
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for DL_GPIO_setLowerPinsPolarity
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for DL_GPIO_enableInterrupt
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for DL_SYSCTL_setBORThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for DL_SYSCTL_setFlashWaitState
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for DL_SYSCTL_setSYSOSCFreq
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for DL_SYSCTL_disableHFXT
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for DL_SYSCTL_disableSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for DL_SYSCTL_setHFCLKSourceHFXTParams
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for DL_SYSCTL_configSYSPLL
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for DL_SYSCTL_setULPCLKDivider
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for DL_SYSCTL_enableMFCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.rodata.gSYSPLLConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for DL_Timer_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for DL_Timer_initFourCCPWMMode
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for DL_Timer_setCounterControl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for DL_Timer_setCaptureCompareOutCtl
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for DL_Timer_setCaptCompUpdateMethod
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for DL_Timer_enableClock
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for DL_Timer_setCCPDirection
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for DL_I2C_setAnalogGlitchFilterPulseWidth
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for DL_I2C_enableAnalogGlitchFilter
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for DL_I2C_resetControllerTransfer
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for DL_I2C_setTimerPeriod
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for DL_I2C_setControllerTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for DL_I2C_setControllerRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for DL_I2C_enableControllerClockStretching
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for DL_I2C_enableController
    ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.rodata.gIMUClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_IMU_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for DL_I2C_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for DL_I2C_setAnalogGlitchFilterPulseWidth
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for DL_I2C_enableAnalogGlitchFilter
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for DL_I2C_resetControllerTransfer
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for DL_I2C_setTimerPeriod
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for DL_I2C_setControllerTXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for DL_I2C_setControllerRXFIFOThreshold
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for DL_I2C_enableControllerClockStretching
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for DL_I2C_enableController
    ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.rodata.gGRAYClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GRAY_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.rodata.gUART_0Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.rodata.gUART_1Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.rodata.gUART_2Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to dl_uart.o(.text.DL_UART_setClockConfig) for DL_UART_setClockConfig
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to dl_uart.o(.text.DL_UART_init) for DL_UART_init
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for DL_UART_setOversampling
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for DL_UART_setBaudRateDivisor
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for DL_UART_enableInterrupt
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for DL_UART_enable
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.rodata.gUART_3ClockConfig) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.rodata.gUART_3Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_3_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SysTick_Config) for SysTick_Config
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus) for DL_SYSCTL_getClockStatus
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for DL_Timer_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for DL_UART_Main_saveConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup) for gMOTOR_PWM_RIGHTBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_3Backup) for gUART_3Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for DL_Timer_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for DL_UART_Main_restoreConfiguration
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup) for gMOTOR_PWM_RIGHTBackup
    ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.bss.gUART_3Backup) for gUART_3Backup
    ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset) refers to ti_msp_dl_config.o(.text.DL_GPIO_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset) refers to ti_msp_dl_config.o(.text.DL_Timer_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset) refers to ti_msp_dl_config.o(.text.DL_I2C_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset) refers to ti_msp_dl_config.o(.text.DL_UART_reset) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower) refers to ti_msp_dl_config.o(.text.DL_GPIO_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower) refers to ti_msp_dl_config.o(.text.DL_Timer_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower) refers to ti_msp_dl_config.o(.text.DL_I2C_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower) refers to ti_msp_dl_config.o(.text.DL_UART_enablePower) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction) refers to ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput) refers to ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity) refers to ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_getClockStatus) refers to ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl) refers to ti_msp_dl_config.o(.text.DL_Timer_setCounterControl) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock) refers to ti_msp_dl_config.o(.text.DL_Timer_enableClock) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection) refers to ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth) refers to ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter) refers to ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer) refers to ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod) refers to ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold) refers to ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching) refers to ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController) refers to ti_msp_dl_config.o(.text.DL_I2C_enableController) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling) refers to ti_msp_dl_config.o(.text.DL_UART_setOversampling) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor) refers to ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt) refers to ti_msp_dl_config.o(.text.DL_UART_enableInterrupt) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable) refers to ti_msp_dl_config.o(.text.DL_UART_enable) for [Anonymous Symbol]
    ti_msp_dl_config.o(.text.SysTick_Config) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for __NVIC_SetPriority
    ti_msp_dl_config.o(.ARM.exidx.text.SysTick_Config) refers to ti_msp_dl_config.o(.text.SysTick_Config) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg) refers to ti_msp_dl_config.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority) refers to ti_msp_dl_config.o(.text.__NVIC_SetPriority) for [Anonymous Symbol]
    main.o(.text.main) refers to ti_msp_dl_config.o(.text.SYSCFG_DL_init) for SYSCFG_DL_init
    main.o(.text.main) refers to uart_driver.o(.text.my_printf) for my_printf
    main.o(.text.main) refers to main.o(.text.user_config) for user_config
    main.o(.text.main) refers to encoder_driver.o(.text.encoder_config) for encoder_config
    main.o(.text.main) refers to scheduler.o(.text.scheduler_init) for scheduler_init
    main.o(.text.main) refers to scheduler.o(.text.scheduler_run) for scheduler_run
    main.o(.text.main) refers to main.o(.rodata.str1.1) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.main) refers to main.o(.text.main) for [Anonymous Symbol]
    main.o(.text.user_config) refers to main.o(.text.__NVIC_ClearPendingIRQ) for __NVIC_ClearPendingIRQ
    main.o(.text.user_config) refers to main.o(.text.__NVIC_EnableIRQ) for __NVIC_EnableIRQ
    main.o(.ARM.exidx.text.user_config) refers to main.o(.text.user_config) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ) refers to main.o(.text.__NVIC_ClearPendingIRQ) for [Anonymous Symbol]
    main.o(.ARM.exidx.text.__NVIC_EnableIRQ) refers to main.o(.text.__NVIC_EnableIRQ) for [Anonymous Symbol]
    uart_driver.o(.text.uart_send_char) refers to uart_driver.o(.text.DL_UART_isBusy) for DL_UART_isBusy
    uart_driver.o(.text.uart_send_char) refers to uart_driver.o(.text.DL_UART_transmitData) for DL_UART_transmitData
    uart_driver.o(.ARM.exidx.text.uart_send_char) refers to uart_driver.o(.text.uart_send_char) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_isBusy) refers to uart_driver.o(.text.DL_UART_isBusy) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_transmitData) refers to uart_driver.o(.text.DL_UART_transmitData) for [Anonymous Symbol]
    uart_driver.o(.text.uart_send_string) refers to uart_driver.o(.text.uart_send_char) for uart_send_char
    uart_driver.o(.ARM.exidx.text.uart_send_string) refers to uart_driver.o(.text.uart_send_string) for [Anonymous Symbol]
    uart_driver.o(.text.my_printf) refers to printfa.o(i.__0vsnprintf) for vsnprintf
    uart_driver.o(.text.my_printf) refers to uart_driver.o(.text.uart_send_string) for uart_send_string
    uart_driver.o(.ARM.exidx.text.my_printf) refers to uart_driver.o(.text.my_printf) for [Anonymous Symbol]
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.text.UART0_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART0_IRQHandler) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.ARM.exidx.text.UART0_IRQHandler) refers to uart_driver.o(.text.UART0_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_getPendingInterrupt) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for [Anonymous Symbol]
    uart_driver.o(.ARM.exidx.text.DL_UART_receiveData) refers to uart_driver.o(.text.DL_UART_receiveData) for [Anonymous Symbol]
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index_1) for uart_rx_index_1
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer_1) for uart_rx_buffer_1
    uart_driver.o(.text.UART1_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART1_IRQHandler) refers to uart_driver.o(.bss.uart_tick_1) for uart_tick_1
    uart_driver.o(.ARM.exidx.text.UART1_IRQHandler) refers to uart_driver.o(.text.UART1_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.UART2_IRQHandler) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_driver.o(.text.UART2_IRQHandler) refers to uart_driver.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_driver.o(.text.UART2_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index_2) for uart_rx_index_2
    uart_driver.o(.text.UART2_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer_2) for uart_rx_buffer_2
    uart_driver.o(.text.UART2_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART2_IRQHandler) refers to uart_driver.o(.bss.uart_tick_2) for uart_tick_2
    uart_driver.o(.ARM.exidx.text.UART2_IRQHandler) refers to uart_driver.o(.text.UART2_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.UART3_IRQHandler) refers to uart_driver.o(.text.DL_UART_getPendingInterrupt) for DL_UART_getPendingInterrupt
    uart_driver.o(.text.UART3_IRQHandler) refers to uart_driver.o(.text.DL_UART_receiveData) for DL_UART_receiveData
    uart_driver.o(.text.UART3_IRQHandler) refers to uart_driver.o(.bss.uart_rx_index_3) for uart_rx_index_3
    uart_driver.o(.text.UART3_IRQHandler) refers to uart_driver.o(.bss.uart_rx_buffer_3) for uart_rx_buffer_3
    uart_driver.o(.text.UART3_IRQHandler) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.UART3_IRQHandler) refers to uart_driver.o(.bss.uart_tick_3) for uart_tick_3
    uart_driver.o(.ARM.exidx.text.UART3_IRQHandler) refers to uart_driver.o(.text.UART3_IRQHandler) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart0_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_index) for uart_rx_index
    uart_driver.o(.text.uart0_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_tick) for uart_tick
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart0_task) refers to uart_driver.o(.bss.uart_rx_buffer) for uart_rx_buffer
    uart_driver.o(.ARM.exidx.text.uart0_task) refers to uart_driver.o(.text.uart0_task) for [Anonymous Symbol]
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart1_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_rx_index_1) for uart_rx_index_1
    uart_driver.o(.text.uart1_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_tick_1) for uart_tick_1
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart1_task) refers to uart_driver.o(.bss.uart_rx_buffer_1) for uart_rx_buffer_1
    uart_driver.o(.ARM.exidx.text.uart1_task) refers to uart_driver.o(.text.uart1_task) for [Anonymous Symbol]
    uart_driver.o(.text.uart2_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart2_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_driver.o(.text.uart2_task) refers to uart_driver.o(.bss.uart_rx_index_2) for uart_rx_index_2
    uart_driver.o(.text.uart2_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart2_task) refers to uart_driver.o(.bss.uart_tick_2) for uart_tick_2
    uart_driver.o(.text.uart2_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart2_task) refers to uart_driver.o(.bss.uart_rx_buffer_2) for uart_rx_buffer_2
    uart_driver.o(.ARM.exidx.text.uart2_task) refers to uart_driver.o(.text.uart2_task) for [Anonymous Symbol]
    uart_driver.o(.text.uart3_task) refers to uart_driver.o(.text.my_printf) for my_printf
    uart_driver.o(.text.uart3_task) refers to memseta.o(.text) for __aeabi_memclr
    uart_driver.o(.text.uart3_task) refers to uart_driver.o(.bss.uart_rx_index_3) for uart_rx_index_3
    uart_driver.o(.text.uart3_task) refers to scheduler.o(.bss.uwTick) for uwTick
    uart_driver.o(.text.uart3_task) refers to uart_driver.o(.bss.uart_tick_3) for uart_tick_3
    uart_driver.o(.text.uart3_task) refers to uart_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    uart_driver.o(.text.uart3_task) refers to uart_driver.o(.bss.uart_rx_buffer_3) for uart_rx_buffer_3
    uart_driver.o(.ARM.exidx.text.uart3_task) refers to uart_driver.o(.text.uart3_task) for [Anonymous Symbol]
    button_driver.o(.text.key_read) refers to button_driver.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    button_driver.o(.ARM.exidx.text.key_read) refers to button_driver.o(.text.key_read) for [Anonymous Symbol]
    button_driver.o(.ARM.exidx.text.DL_GPIO_readPins) refers to button_driver.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    button_driver.o(.text.key_task) refers to button_driver.o(.text.key_read) for key_read
    button_driver.o(.text.key_task) refers to uart_driver.o(.text.my_printf) for my_printf
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_val) for key_val
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_old) for key_old
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_down) for key_down
    button_driver.o(.text.key_task) refers to button_driver.o(.bss.key_up) for key_up
    button_driver.o(.text.key_task) refers to button_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    button_driver.o(.ARM.exidx.text.key_task) refers to button_driver.o(.text.key_task) for [Anonymous Symbol]
    iic_driver.o(.text.I2C_WriteByte) refers to iic_driver.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic_driver.o(.text.I2C_WriteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic_driver.o(.text.I2C_WriteByte) refers to iic_driver.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic_driver.o(.ARM.exidx.text.I2C_WriteByte) refers to iic_driver.o(.text.I2C_WriteByte) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_I2C_getControllerStatus) refers to iic_driver.o(.text.DL_I2C_getControllerStatus) for [Anonymous Symbol]
    iic_driver.o(.text.DL_I2C_startControllerTransfer) refers to iic_driver.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    iic_driver.o(.ARM.exidx.text.DL_I2C_startControllerTransfer) refers to iic_driver.o(.text.DL_I2C_startControllerTransfer) for [Anonymous Symbol]
    iic_driver.o(.text.I2C_WriteMultiBytes) refers to iic_driver.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic_driver.o(.text.I2C_WriteMultiBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic_driver.o(.text.I2C_WriteMultiBytes) refers to iic_driver.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic_driver.o(.ARM.exidx.text.I2C_WriteMultiBytes) refers to iic_driver.o(.text.I2C_WriteMultiBytes) for [Anonymous Symbol]
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_transmitControllerData) for DL_I2C_transmitControllerData
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_clearInterruptStatus) for DL_I2C_clearInterruptStatus
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_isControllerRXFIFOEmpty) for DL_I2C_isControllerRXFIFOEmpty
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    iic_driver.o(.text.IIC_Read) refers to iic_driver.o(.text.DL_I2C_getRawInterruptStatus) for DL_I2C_getRawInterruptStatus
    iic_driver.o(.text.IIC_Read) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic_driver.o(.ARM.exidx.text.IIC_Read) refers to iic_driver.o(.text.IIC_Read) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_I2C_transmitControllerData) refers to iic_driver.o(.text.DL_I2C_transmitControllerData) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_I2C_clearInterruptStatus) refers to iic_driver.o(.text.DL_I2C_clearInterruptStatus) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty) refers to iic_driver.o(.text.DL_I2C_isControllerRXFIFOEmpty) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_I2C_receiveControllerData) refers to iic_driver.o(.text.DL_I2C_receiveControllerData) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_I2C_getRawInterruptStatus) refers to iic_driver.o(.text.DL_I2C_getRawInterruptStatus) for [Anonymous Symbol]
    iic_driver.o(.text.BNO080_IIC_Read) refers to iic_driver.o(.text.DL_I2C_clearInterruptStatus) for DL_I2C_clearInterruptStatus
    iic_driver.o(.text.BNO080_IIC_Read) refers to iic_driver.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic_driver.o(.text.BNO080_IIC_Read) refers to iic_driver.o(.text.DL_I2C_isControllerRXFIFOEmpty) for DL_I2C_isControllerRXFIFOEmpty
    iic_driver.o(.text.BNO080_IIC_Read) refers to iic_driver.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    iic_driver.o(.text.BNO080_IIC_Read) refers to iic_driver.o(.text.DL_I2C_getRawInterruptStatus) for DL_I2C_getRawInterruptStatus
    iic_driver.o(.text.BNO080_IIC_Read) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic_driver.o(.ARM.exidx.text.BNO080_IIC_Read) refers to iic_driver.o(.text.BNO080_IIC_Read) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.ShortToChar) refers to iic_driver.o(.text.ShortToChar) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.CharToShort) refers to iic_driver.o(.text.CharToShort) for [Anonymous Symbol]
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.text.IIC_Read) for IIC_Read
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.text.CharToShort) for CharToShort
    iic_driver.o(.text.iic_test) refers to fflti.o(.text) for __aeabi_i2f
    iic_driver.o(.text.iic_test) refers to fdiv.o(.text) for __aeabi_fdiv
    iic_driver.o(.text.iic_test) refers to fmul.o(.text) for __aeabi_fmul
    iic_driver.o(.text.iic_test) refers to f2d.o(.text) for __aeabi_f2d
    iic_driver.o(.text.iic_test) refers to uart_driver.o(.text.my_printf) for my_printf
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.bss.chrTemp) for chrTemp
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.bss.a) for a
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.bss.w) for w
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.bss.h) for h
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.bss.Angle) for Angle
    iic_driver.o(.text.iic_test) refers to iic_driver.o(.rodata.str1.1) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.iic_test) refers to iic_driver.o(.text.iic_test) for [Anonymous Symbol]
    iic_driver.o(.ARM.exidx.text.DL_Common_updateReg) refers to iic_driver.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    encoder_driver.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.DL_Interrupt_getPendingGroup) for DL_Interrupt_getPendingGroup
    encoder_driver.o(.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.encoder_func) for encoder_func
    encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler) refers to encoder_driver.o(.text.GROUP1_IRQHandler) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_Interrupt_getPendingGroup) refers to encoder_driver.o(.text.DL_Interrupt_getPendingGroup) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus) for DL_GPIO_getEnabledInterruptStatus
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.text.DL_GPIO_readPins) for DL_GPIO_readPins
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.text.DL_GPIO_clearInterruptStatus) for DL_GPIO_clearInterruptStatus
    encoder_driver.o(.text.encoder_func) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.encoder_func) refers to encoder_driver.o(.text.encoder_func) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.encoder_init) refers to encoder_driver.o(.text.encoder_init) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_update) refers to fflti.o(.text) for __aeabi_i2f
    encoder_driver.o(.text.encoder_update) refers to fdiv.o(.text) for __aeabi_fdiv
    encoder_driver.o(.text.encoder_update) refers to fmul.o(.text) for __aeabi_fmul
    encoder_driver.o(.text.encoder_update) refers to encoder_driver.o(.bss.encoder_get_count) for encoder_get_count
    encoder_driver.o(.ARM.exidx.text.encoder_update) refers to encoder_driver.o(.text.encoder_update) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.text.encoder_update) for encoder_update
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_task) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_task) refers to encoder_driver.o(.text.encoder_task) for [Anonymous Symbol]
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.text.encoder_init) for encoder_init
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_left) for encoder_left
    encoder_driver.o(.text.encoder_config) refers to encoder_driver.o(.bss.encoder_right) for encoder_right
    encoder_driver.o(.ARM.exidx.text.encoder_config) refers to encoder_driver.o(.text.encoder_config) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus) refers to encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_GPIO_readPins) refers to encoder_driver.o(.text.DL_GPIO_readPins) for [Anonymous Symbol]
    encoder_driver.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus) refers to encoder_driver.o(.text.DL_GPIO_clearInterruptStatus) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Create) refers to motor_driver.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor_driver.o(.text.Motor_Create) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_Create) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Create) refers to motor_driver.o(.text.Motor_Create) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.DL_GPIO_setPins) refers to motor_driver.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to motor_driver.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.text.Motor_SetSpeed) refers to fcmplt.o(.text) for __aeabi_fcmplt
    motor_driver.o(.text.Motor_SetSpeed) refers to fcmple.o(.text) for __aeabi_fcmple
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.Float_To_Speed1000) for Float_To_Speed1000
    motor_driver.o(.text.Motor_SetSpeed) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_SetSpeed) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.text.Motor_SetSpeed) refers to fflti.o(.text) for __aeabi_i2f
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.Speed_To_PWM) for Speed_To_PWM
    motor_driver.o(.text.Motor_SetSpeed) refers to motor_driver.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    motor_driver.o(.ARM.exidx.text.Motor_SetSpeed) refers to motor_driver.o(.text.Motor_SetSpeed) for [Anonymous Symbol]
    motor_driver.o(.ARM.exidx.text.Motor_ValidateParams) refers to motor_driver.o(.text.Motor_ValidateParams) for [Anonymous Symbol]
    motor_driver.o(.text.Float_To_Speed1000) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Float_To_Speed1000) refers to roundf.o(i.roundf) for roundf
    motor_driver.o(.text.Float_To_Speed1000) refers to ffixi.o(.text) for __aeabi_f2iz
    motor_driver.o(.ARM.exidx.text.Float_To_Speed1000) refers to motor_driver.o(.text.Float_To_Speed1000) for [Anonymous Symbol]
    motor_driver.o(.text.Speed_To_PWM) refers to fcmpge.o(.text) for __aeabi_fcmpge
    motor_driver.o(.text.Speed_To_PWM) refers to fcmpeq.o(.text) for __aeabi_fcmpeq
    motor_driver.o(.text.Speed_To_PWM) refers to fmul.o(.text) for __aeabi_fmul
    motor_driver.o(.text.Speed_To_PWM) refers to fdiv.o(.text) for __aeabi_fdiv
    motor_driver.o(.text.Speed_To_PWM) refers to ffixui.o(.text) for __aeabi_f2uiz
    motor_driver.o(.ARM.exidx.text.Speed_To_PWM) refers to motor_driver.o(.text.Speed_To_PWM) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Stop) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.text.Motor_Stop) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_Stop) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Stop) refers to motor_driver.o(.text.Motor_Stop) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_GetState) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.ARM.exidx.text.Motor_GetState) refers to motor_driver.o(.text.Motor_GetState) for [Anonymous Symbol]
    motor_driver.o(.text.Motor_Enable) refers to motor_driver.o(.text.Motor_ValidateParams) for Motor_ValidateParams
    motor_driver.o(.text.Motor_Enable) refers to motor_driver.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    motor_driver.o(.text.Motor_Enable) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for DL_Timer_setCaptureCompareValue
    motor_driver.o(.ARM.exidx.text.Motor_Enable) refers to motor_driver.o(.text.Motor_Enable) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_init) refers to pid.o(.text.pid_init) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_target) refers to pid.o(.text.pid_set_target) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_params) refers to pid.o(.text.pid_set_params) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_set_limit) refers to pid.o(.text.pid_set_limit) for [Anonymous Symbol]
    pid.o(.ARM.exidx.text.pid_reset) refers to pid.o(.text.pid_reset) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_positional) refers to pid.o(.text.pid_formula_positional) for pid_formula_positional
    pid.o(.text.pid_calculate_positional) refers to pid.o(.text.pid_out_limit) for pid_out_limit
    pid.o(.ARM.exidx.text.pid_calculate_positional) refers to pid.o(.text.pid_calculate_positional) for [Anonymous Symbol]
    pid.o(.text.pid_formula_positional) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_formula_positional) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.ARM.exidx.text.pid_formula_positional) refers to pid.o(.text.pid_formula_positional) for [Anonymous Symbol]
    pid.o(.text.pid_out_limit) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.pid_out_limit) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_out_limit) refers to pid.o(.text.pid_out_limit) for [Anonymous Symbol]
    pid.o(.text.pid_calculate_incremental) refers to pid.o(.text.pid_formula_incremental) for pid_formula_incremental
    pid.o(.text.pid_calculate_incremental) refers to pid.o(.text.pid_out_limit) for pid_out_limit
    pid.o(.ARM.exidx.text.pid_calculate_incremental) refers to pid.o(.text.pid_calculate_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_formula_incremental) refers to fadd.o(.text) for __aeabi_fsub
    pid.o(.text.pid_formula_incremental) refers to fmul.o(.text) for __aeabi_fmul
    pid.o(.ARM.exidx.text.pid_formula_incremental) refers to pid.o(.text.pid_formula_incremental) for [Anonymous Symbol]
    pid.o(.text.pid_constrain) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.text.pid_constrain) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.ARM.exidx.text.pid_constrain) refers to pid.o(.text.pid_constrain) for [Anonymous Symbol]
    pid.o(.text.pid_app_limit_integral) refers to fcmple.o(.text) for __aeabi_fcmple
    pid.o(.text.pid_app_limit_integral) refers to fcmpge.o(.text) for __aeabi_fcmpge
    pid.o(.ARM.exidx.text.pid_app_limit_integral) refers to pid.o(.text.pid_app_limit_integral) for [Anonymous Symbol]
    bno08x_hal.o(.text.BNO080_HardwareReset) refers to bno08x_hal.o(.text.DL_GPIO_clearPins) for DL_GPIO_clearPins
    bno08x_hal.o(.text.BNO080_HardwareReset) refers to scheduler.o(.text.DL_Delay) for DL_Delay
    bno08x_hal.o(.text.BNO080_HardwareReset) refers to bno08x_hal.o(.text.DL_GPIO_setPins) for DL_GPIO_setPins
    bno08x_hal.o(.text.BNO080_HardwareReset) refers to bno08x_hal.o(.text.receivePacket) for receivePacket
    bno08x_hal.o(.text.BNO080_HardwareReset) refers to scheduler.o(.bss.uwTick) for uwTick
    bno08x_hal.o(.text.BNO080_HardwareReset) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.BNO080_HardwareReset) refers to bno08x_hal.o(.text.BNO080_HardwareReset) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.DL_GPIO_clearPins) refers to bno08x_hal.o(.text.DL_GPIO_clearPins) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.DL_GPIO_setPins) refers to bno08x_hal.o(.text.DL_GPIO_setPins) for [Anonymous Symbol]
    bno08x_hal.o(.text.receivePacket) refers to iic_driver.o(.text.IIC_Read) for IIC_Read
    bno08x_hal.o(.text.receivePacket) refers to bno08x_hal.o(.bss.hi2c_bno080) for [Anonymous Symbol]
    bno08x_hal.o(.text.receivePacket) refers to bno08x_hal.o(.bss._deviceAddress) for [Anonymous Symbol]
    bno08x_hal.o(.text.receivePacket) refers to bno08x_hal.o(.bss.shtpHeader) for [Anonymous Symbol]
    bno08x_hal.o(.text.receivePacket) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.receivePacket) refers to bno08x_hal.o(.text.receivePacket) for [Anonymous Symbol]
    bno08x_hal.o(.text.BNO080_Init) refers to bno08x_hal.o(.bss.hi2c_bno080) for [Anonymous Symbol]
    bno08x_hal.o(.text.BNO080_Init) refers to bno08x_hal.o(.bss._deviceAddress) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.BNO080_Init) refers to bno08x_hal.o(.text.BNO080_Init) for [Anonymous Symbol]
    bno08x_hal.o(.text.softReset) refers to bno08x_hal.o(.text.sendPacket) for sendPacket
    bno08x_hal.o(.text.softReset) refers to scheduler.o(.text.DL_Delay) for DL_Delay
    bno08x_hal.o(.text.softReset) refers to bno08x_hal.o(.text.receivePacket) for receivePacket
    bno08x_hal.o(.text.softReset) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.softReset) refers to bno08x_hal.o(.text.softReset) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendPacket) refers to iic_driver.o(.text.I2C_WriteMultiBytes) for I2C_WriteMultiBytes
    bno08x_hal.o(.text.sendPacket) refers to bno08x_hal.o(.bss.sequenceNumber) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendPacket) refers to bno08x_hal.o(.bss.hi2c_bno080) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendPacket) refers to bno08x_hal.o(.bss._deviceAddress) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendPacket) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.sendPacket) refers to bno08x_hal.o(.text.sendPacket) for [Anonymous Symbol]
    bno08x_hal.o(.text.resetReason) refers to bno08x_hal.o(.text.sendPacket) for sendPacket
    bno08x_hal.o(.text.resetReason) refers to bno08x_hal.o(.text.receivePacket) for receivePacket
    bno08x_hal.o(.text.resetReason) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.resetReason) refers to bno08x_hal.o(.text.resetReason) for [Anonymous Symbol]
    bno08x_hal.o(.text.qToFloat) refers to fflti.o(.text) for __aeabi_i2f
    bno08x_hal.o(.text.qToFloat) refers to powf.o(i.powf) for powf
    bno08x_hal.o(.text.qToFloat) refers to fmul.o(.text) for __aeabi_fmul
    bno08x_hal.o(.ARM.exidx.text.qToFloat) refers to bno08x_hal.o(.text.qToFloat) for [Anonymous Symbol]
    bno08x_hal.o(.text.dataAvailable) refers to bno08x_hal.o(.text.receivePacket) for receivePacket
    bno08x_hal.o(.text.dataAvailable) refers to bno08x_hal.o(.text.parseInputReport) for parseInputReport
    bno08x_hal.o(.text.dataAvailable) refers to bno08x_hal.o(.bss.shtpHeader) for [Anonymous Symbol]
    bno08x_hal.o(.text.dataAvailable) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.dataAvailable) refers to bno08x_hal.o(.text.dataAvailable) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.shtpHeader) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.activityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.data._activityConfidences) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.stabilityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.stepCount) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.quatAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawQuatI) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawQuatJ) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawQuatK) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawQuatReal) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawQuatRadianAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.magAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawMagX) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawMagY) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawMagZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.gyroAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawGyroX) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawGyroY) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawGyroZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.accelLinAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawLinAccelX) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawLinAccelY) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawLinAccelZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.accelAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawAccelX) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawAccelY) for [Anonymous Symbol]
    bno08x_hal.o(.text.parseInputReport) refers to bno08x_hal.o(.bss.rawAccelZ) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.parseInputReport) refers to bno08x_hal.o(.text.parseInputReport) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatI) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getQuatI) refers to bno08x_hal.o(.bss.rawQuatI) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatI) refers to bno08x_hal.o(.data.rotationVector_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQuatI) refers to bno08x_hal.o(.text.getQuatI) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatJ) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getQuatJ) refers to bno08x_hal.o(.bss.rawQuatJ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatJ) refers to bno08x_hal.o(.data.rotationVector_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQuatJ) refers to bno08x_hal.o(.text.getQuatJ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatK) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getQuatK) refers to bno08x_hal.o(.bss.rawQuatK) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatK) refers to bno08x_hal.o(.data.rotationVector_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQuatK) refers to bno08x_hal.o(.text.getQuatK) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatReal) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getQuatReal) refers to bno08x_hal.o(.bss.rawQuatReal) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatReal) refers to bno08x_hal.o(.data.rotationVector_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQuatReal) refers to bno08x_hal.o(.text.getQuatReal) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatRadianAccuracy) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getQuatRadianAccuracy) refers to bno08x_hal.o(.bss.rawQuatRadianAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatRadianAccuracy) refers to bno08x_hal.o(.data.rotationVector_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQuatRadianAccuracy) refers to bno08x_hal.o(.text.getQuatRadianAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQuatAccuracy) refers to bno08x_hal.o(.bss.quatAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQuatAccuracy) refers to bno08x_hal.o(.text.getQuatAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelX) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getAccelX) refers to bno08x_hal.o(.bss.rawAccelX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelX) refers to bno08x_hal.o(.data.accelerometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getAccelX) refers to bno08x_hal.o(.text.getAccelX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelY) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getAccelY) refers to bno08x_hal.o(.bss.rawAccelY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelY) refers to bno08x_hal.o(.data.accelerometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getAccelY) refers to bno08x_hal.o(.text.getAccelY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelZ) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getAccelZ) refers to bno08x_hal.o(.bss.rawAccelZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelZ) refers to bno08x_hal.o(.data.accelerometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getAccelZ) refers to bno08x_hal.o(.text.getAccelZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getAccelAccuracy) refers to bno08x_hal.o(.bss.accelAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getAccelAccuracy) refers to bno08x_hal.o(.text.getAccelAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelX) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getLinAccelX) refers to bno08x_hal.o(.bss.rawLinAccelX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelX) refers to bno08x_hal.o(.data.linear_accelerometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getLinAccelX) refers to bno08x_hal.o(.text.getLinAccelX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelY) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getLinAccelY) refers to bno08x_hal.o(.bss.rawLinAccelY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelY) refers to bno08x_hal.o(.data.linear_accelerometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getLinAccelY) refers to bno08x_hal.o(.text.getLinAccelY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelZ) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getLinAccelZ) refers to bno08x_hal.o(.bss.rawLinAccelZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelZ) refers to bno08x_hal.o(.data.linear_accelerometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getLinAccelZ) refers to bno08x_hal.o(.text.getLinAccelZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getLinAccelAccuracy) refers to bno08x_hal.o(.bss.accelLinAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getLinAccelAccuracy) refers to bno08x_hal.o(.text.getLinAccelAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroX) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getGyroX) refers to bno08x_hal.o(.bss.rawGyroX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroX) refers to bno08x_hal.o(.data.gyro_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getGyroX) refers to bno08x_hal.o(.text.getGyroX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroY) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getGyroY) refers to bno08x_hal.o(.bss.rawGyroY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroY) refers to bno08x_hal.o(.data.gyro_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getGyroY) refers to bno08x_hal.o(.text.getGyroY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroZ) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getGyroZ) refers to bno08x_hal.o(.bss.rawGyroZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroZ) refers to bno08x_hal.o(.data.gyro_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getGyroZ) refers to bno08x_hal.o(.text.getGyroZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getGyroAccuracy) refers to bno08x_hal.o(.bss.gyroAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getGyroAccuracy) refers to bno08x_hal.o(.text.getGyroAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagX) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getMagX) refers to bno08x_hal.o(.bss.rawMagX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagX) refers to bno08x_hal.o(.data.magnetometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getMagX) refers to bno08x_hal.o(.text.getMagX) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagY) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getMagY) refers to bno08x_hal.o(.bss.rawMagY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagY) refers to bno08x_hal.o(.data.magnetometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getMagY) refers to bno08x_hal.o(.text.getMagY) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagZ) refers to bno08x_hal.o(.text.qToFloat) for qToFloat
    bno08x_hal.o(.text.getMagZ) refers to bno08x_hal.o(.bss.rawMagZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagZ) refers to bno08x_hal.o(.data.magnetometer_Q1) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getMagZ) refers to bno08x_hal.o(.text.getMagZ) for [Anonymous Symbol]
    bno08x_hal.o(.text.getMagAccuracy) refers to bno08x_hal.o(.bss.magAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getMagAccuracy) refers to bno08x_hal.o(.text.getMagAccuracy) for [Anonymous Symbol]
    bno08x_hal.o(.text.getStepCount) refers to bno08x_hal.o(.bss.stepCount) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getStepCount) refers to bno08x_hal.o(.text.getStepCount) for [Anonymous Symbol]
    bno08x_hal.o(.text.getStabilityClassifier) refers to bno08x_hal.o(.bss.stabilityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getStabilityClassifier) refers to bno08x_hal.o(.text.getStabilityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.text.getActivityClassifier) refers to bno08x_hal.o(.bss.activityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getActivityClassifier) refers to bno08x_hal.o(.text.getActivityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.text.calibrateAccelerometer) refers to bno08x_hal.o(.text.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(.ARM.exidx.text.calibrateAccelerometer) refers to bno08x_hal.o(.text.calibrateAccelerometer) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendCalibrateCommand) refers to bno08x_hal.o(.text.sendCommand) for sendCommand
    bno08x_hal.o(.text.sendCalibrateCommand) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.sendCalibrateCommand) refers to bno08x_hal.o(.text.sendCalibrateCommand) for [Anonymous Symbol]
    bno08x_hal.o(.text.calibrateGyro) refers to bno08x_hal.o(.text.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(.ARM.exidx.text.calibrateGyro) refers to bno08x_hal.o(.text.calibrateGyro) for [Anonymous Symbol]
    bno08x_hal.o(.text.calibrateMagnetometer) refers to bno08x_hal.o(.text.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(.ARM.exidx.text.calibrateMagnetometer) refers to bno08x_hal.o(.text.calibrateMagnetometer) for [Anonymous Symbol]
    bno08x_hal.o(.text.calibratePlanarAccelerometer) refers to bno08x_hal.o(.text.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(.ARM.exidx.text.calibratePlanarAccelerometer) refers to bno08x_hal.o(.text.calibratePlanarAccelerometer) for [Anonymous Symbol]
    bno08x_hal.o(.text.calibrateAll) refers to bno08x_hal.o(.text.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(.ARM.exidx.text.calibrateAll) refers to bno08x_hal.o(.text.calibrateAll) for [Anonymous Symbol]
    bno08x_hal.o(.text.endCalibration) refers to bno08x_hal.o(.text.sendCalibrateCommand) for sendCalibrateCommand
    bno08x_hal.o(.ARM.exidx.text.endCalibration) refers to bno08x_hal.o(.text.endCalibration) for [Anonymous Symbol]
    bno08x_hal.o(.text.saveCalibration) refers to bno08x_hal.o(.text.sendCommand) for sendCommand
    bno08x_hal.o(.text.saveCalibration) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.saveCalibration) refers to bno08x_hal.o(.text.saveCalibration) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendCommand) refers to bno08x_hal.o(.text.sendPacket) for sendPacket
    bno08x_hal.o(.text.sendCommand) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.text.sendCommand) refers to bno08x_hal.o(.bss.commandSequenceNumber) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.sendCommand) refers to bno08x_hal.o(.text.sendCommand) for [Anonymous Symbol]
    bno08x_hal.o(.text.setFeatureCommand) refers to bno08x_hal.o(.text.sendPacket) for sendPacket
    bno08x_hal.o(.text.setFeatureCommand) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.setFeatureCommand) refers to bno08x_hal.o(.text.setFeatureCommand) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableRotationVector) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableRotationVector) refers to bno08x_hal.o(.text.enableRotationVector) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableGameRotationVector) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableGameRotationVector) refers to bno08x_hal.o(.text.enableGameRotationVector) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableAccelerometer) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableAccelerometer) refers to bno08x_hal.o(.text.enableAccelerometer) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableLinearAccelerometer) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableLinearAccelerometer) refers to bno08x_hal.o(.text.enableLinearAccelerometer) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableGyro) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableGyro) refers to bno08x_hal.o(.text.enableGyro) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableMagnetometer) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableMagnetometer) refers to bno08x_hal.o(.text.enableMagnetometer) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableStepCounter) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableStepCounter) refers to bno08x_hal.o(.text.enableStepCounter) for [Anonymous Symbol]
    bno08x_hal.o(.text.enableStabilityClassifier) refers to bno08x_hal.o(.text.setFeatureCommand) for setFeatureCommand
    bno08x_hal.o(.ARM.exidx.text.enableStabilityClassifier) refers to bno08x_hal.o(.text.enableStabilityClassifier) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQ1) refers to bno08x_hal.o(.text.readFRSdata) for readFRSdata
    bno08x_hal.o(.text.getQ1) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQ1) refers to bno08x_hal.o(.text.getQ1) for [Anonymous Symbol]
    bno08x_hal.o(.text.readFRSdata) refers to bno08x_hal.o(.text.frsReadRequest) for frsReadRequest
    bno08x_hal.o(.text.readFRSdata) refers to scheduler.o(.text.DL_Delay) for DL_Delay
    bno08x_hal.o(.text.readFRSdata) refers to bno08x_hal.o(.text.receivePacket) for receivePacket
    bno08x_hal.o(.text.readFRSdata) refers to bno08x_hal.o(.bss.shtpHeader) for [Anonymous Symbol]
    bno08x_hal.o(.text.readFRSdata) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.text.readFRSdata) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.readFRSdata) refers to bno08x_hal.o(.text.readFRSdata) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQ2) refers to bno08x_hal.o(.text.readFRSdata) for readFRSdata
    bno08x_hal.o(.text.getQ2) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQ2) refers to bno08x_hal.o(.text.getQ2) for [Anonymous Symbol]
    bno08x_hal.o(.text.getQ3) refers to bno08x_hal.o(.text.readFRSdata) for readFRSdata
    bno08x_hal.o(.text.getQ3) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getQ3) refers to bno08x_hal.o(.text.getQ3) for [Anonymous Symbol]
    bno08x_hal.o(.text.getResolution) refers to bno08x_hal.o(.text.readFRSdata) for readFRSdata
    bno08x_hal.o(.text.getResolution) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getResolution) refers to bno08x_hal.o(.text.getResolution) for [Anonymous Symbol]
    bno08x_hal.o(.text.getRange) refers to bno08x_hal.o(.text.readFRSdata) for readFRSdata
    bno08x_hal.o(.text.getRange) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.getRange) refers to bno08x_hal.o(.text.getRange) for [Anonymous Symbol]
    bno08x_hal.o(.text.readFRSword) refers to bno08x_hal.o(.text.readFRSdata) for readFRSdata
    bno08x_hal.o(.text.readFRSword) refers to bno08x_hal.o(.bss.metaData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.readFRSword) refers to bno08x_hal.o(.text.readFRSword) for [Anonymous Symbol]
    bno08x_hal.o(.text.frsReadRequest) refers to bno08x_hal.o(.text.sendPacket) for sendPacket
    bno08x_hal.o(.text.frsReadRequest) refers to bno08x_hal.o(.bss.shtpData) for [Anonymous Symbol]
    bno08x_hal.o(.ARM.exidx.text.frsReadRequest) refers to bno08x_hal.o(.text.frsReadRequest) for [Anonymous Symbol]
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to fmul.o(.text) for __aeabi_fmul
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to fadd.o(.text) for __aeabi_fadd
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to sqrtf.o(i.sqrtf) for sqrtf
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to fdiv.o(.text) for __aeabi_fdiv
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to fcmplt.o(.text) for __aeabi_fcmplt
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to fcmpge.o(.text) for __aeabi_fcmpge
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to dflti.o(.text) for __aeabi_i2d
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to dmul.o(.text) for __aeabi_dmul
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to ddiv.o(.text) for __aeabi_ddiv
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to d2f.o(.text) for __aeabi_d2f
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to asinf.o(i.asinf) for asinf
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to f2d.o(.text) for __aeabi_f2d
    bno08x_hal.o(.text.QuaternionToEulerAngles) refers to atan2f.o(i.atan2f) for atan2f
    bno08x_hal.o(.ARM.exidx.text.QuaternionToEulerAngles) refers to bno08x_hal.o(.text.QuaternionToEulerAngles) for [Anonymous Symbol]
    bno08x_hal.o(.data._activityConfidences) refers to bno08x_hal.o(.bss.activityConfidences) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadByte) refers to iic.o(.text.hardware_IIC_ReadByte) for hardware_IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_ReadByte) refers to hardware_iic.o(.text.IIC_ReadByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_ReadBytes) refers to iic.o(.text.hardware_IIC_ReadBytes) for hardware_IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes) refers to hardware_iic.o(.text.IIC_ReadBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteByte) refers to iic.o(.text.hardware_IIC_WirteByte) for hardware_IIC_WirteByte
    hardware_iic.o(.ARM.exidx.text.IIC_WriteByte) refers to hardware_iic.o(.text.IIC_WriteByte) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_WriteBytes) refers to iic.o(.text.hardware_IIC_WirteBytes) for hardware_IIC_WirteBytes
    hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes) refers to hardware_iic.o(.text.IIC_WriteBytes) for [Anonymous Symbol]
    hardware_iic.o(.text.Ping) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.Ping) refers to hardware_iic.o(.text.Ping) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_ReadByte) for IIC_ReadByte
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal) refers to hardware_iic.o(.text.IIC_Get_Digtal) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog) refers to hardware_iic.o(.text.IIC_Get_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog) refers to hardware_iic.o(.text.IIC_Get_Single_Anolog) for [Anonymous Symbol]
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_WriteBytes) for IIC_WriteBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to time.o(.text.delay_ms) for delay_ms
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_ReadBytes) for IIC_ReadBytes
    hardware_iic.o(.text.IIC_Get_Normalize) refers to hardware_iic.o(.bss.IIC_write_buff) for IIC_write_buff
    hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize) refers to hardware_iic.o(.text.IIC_Get_Normalize) for [Anonymous Symbol]
    hardware_iic.o(.text.i2c_write) refers to iic.o(.text.hardware_IIC_WirteBytes) for hardware_IIC_WirteBytes
    hardware_iic.o(.ARM.exidx.text.i2c_write) refers to hardware_iic.o(.text.i2c_write) for [Anonymous Symbol]
    hardware_iic.o(.text.i2c_reset) refers to hardware_iic.o(.text.i2c_write) for i2c_write
    hardware_iic.o(.text.i2c_reset) refers to hardware_iic.o(.data.reset_magic_number) for reset_magic_number
    hardware_iic.o(.ARM.exidx.text.i2c_reset) refers to hardware_iic.o(.text.i2c_reset) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_WirteByte) refers to iic.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic.o(.text.hardware_IIC_WirteByte) refers to iic.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic.o(.text.hardware_IIC_WirteByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_WirteByte) refers to iic.o(.text.hardware_IIC_WirteByte) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_I2C_getControllerStatus) refers to iic.o(.text.DL_I2C_getControllerStatus) for [Anonymous Symbol]
    iic.o(.text.DL_I2C_startControllerTransfer) refers to iic.o(.text.DL_Common_updateReg) for DL_Common_updateReg
    iic.o(.ARM.exidx.text.DL_I2C_startControllerTransfer) refers to iic.o(.text.DL_I2C_startControllerTransfer) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_WirteBytes) refers to memcpya.o(.text) for __aeabi_memcpy
    iic.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_WirteBytes) refers to iic.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic.o(.text.hardware_IIC_WirteBytes) refers to iic.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic.o(.text.hardware_IIC_WirteBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.ARM.exidx.text.hardware_IIC_WirteBytes) refers to iic.o(.text.hardware_IIC_WirteBytes) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadByte) refers to iic.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic.o(.text.hardware_IIC_ReadByte) refers to iic.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic.o(.text.hardware_IIC_ReadByte) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadByte) refers to iic.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    iic.o(.ARM.exidx.text.hardware_IIC_ReadByte) refers to iic.o(.text.hardware_IIC_ReadByte) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_I2C_receiveControllerData) refers to iic.o(.text.DL_I2C_receiveControllerData) for [Anonymous Symbol]
    iic.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for DL_I2C_fillControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadBytes) refers to iic.o(.text.DL_I2C_getControllerStatus) for DL_I2C_getControllerStatus
    iic.o(.text.hardware_IIC_ReadBytes) refers to iic.o(.text.DL_I2C_startControllerTransfer) for DL_I2C_startControllerTransfer
    iic.o(.text.hardware_IIC_ReadBytes) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for DL_I2C_flushControllerTXFIFO
    iic.o(.text.hardware_IIC_ReadBytes) refers to iic.o(.text.DL_I2C_receiveControllerData) for DL_I2C_receiveControllerData
    iic.o(.ARM.exidx.text.hardware_IIC_ReadBytes) refers to iic.o(.text.hardware_IIC_ReadBytes) for [Anonymous Symbol]
    iic.o(.ARM.exidx.text.DL_Common_updateReg) refers to iic.o(.text.DL_Common_updateReg) for [Anonymous Symbol]
    time.o(.ARM.exidx.text.delay_us) refers to time.o(.text.delay_us) for [Anonymous Symbol]
    time.o(.text.delay_ms) refers to time.o(.text.delay_us) for delay_us
    time.o(.ARM.exidx.text.delay_ms) refers to time.o(.text.delay_ms) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_init) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.ARM.exidx.text.scheduler_init) refers to scheduler.o(.text.scheduler_init) for [Anonymous Symbol]
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.task_num) for task_num
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.text.scheduler_run) refers to scheduler.o(.data.scheduler_task) for [Anonymous Symbol]
    scheduler.o(.ARM.exidx.text.scheduler_run) refers to scheduler.o(.text.scheduler_run) for [Anonymous Symbol]
    scheduler.o(.text.SysTick_Handler) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.SysTick_Handler) refers to scheduler.o(.text.SysTick_Handler) for [Anonymous Symbol]
    scheduler.o(.text.DL_Delay) refers to scheduler.o(.bss.uwTick) for uwTick
    scheduler.o(.ARM.exidx.text.DL_Delay) refers to scheduler.o(.text.DL_Delay) for [Anonymous Symbol]
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart0_task) for uart0_task
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart1_task) for uart1_task
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart2_task) for uart2_task
    scheduler.o(.data.scheduler_task) refers to uart_driver.o(.text.uart3_task) for uart3_task
    scheduler.o(.data.scheduler_task) refers to button_driver.o(.text.key_task) for key_task
    scheduler.o(.data.scheduler_task) refers to encoder_driver.o(.text.encoder_task) for encoder_task
    dl_common.o(.ARM.exidx.text.DL_Common_delayCycles) refers to dl_common.o(.text.DL_Common_delayCycles) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig) refers to dl_i2c.o(.text.DL_I2C_setClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig) refers to dl_i2c.o(.text.DL_I2C_getClockConfig) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO) refers to dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking) for [Anonymous Symbol]
    dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck) refers to dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig) refers to dl_timer.o(.text.DL_Timer_setClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig) refers to dl_timer.o(.text.DL_Timer_getClockConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode) refers to dl_timer.o(.text.DL_Timer_initTimerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode) refers to dl_timer.o(.text.DL_Timer_initCaptureMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCaptureTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode) refers to dl_timer.o(.text.DL_Timer_initCaptureCombinedMode) for [Anonymous Symbol]
    dl_timer.o(.text.DL_Timer_initCompareMode) refers to dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode) refers to dl_timer.o(.text.DL_Timer_initCompareMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode) refers to dl_timer.o(.text.DL_Timer_initCompareTriggerMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareValue) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompSrcUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionDn) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_setSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp) refers to dl_timer.o(.text.DL_Timer_getSecondCompActionUp) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent) refers to dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod) refers to dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareAction) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut) refers to dl_timer.o(.text.DL_Timer_overrideCCPOut) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInput) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter) refers to dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled) refers to dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration) refers to dl_timer.o(.text.DL_Timer_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration) refers to dl_timer.o(.text.DL_Timer_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode) refers to dl_timer.o(.text.DL_Timer_initFourCCPWMMode) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_setFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig) refers to dl_timer.o(.text.DL_Timer_getFaultSourceConfig) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration) refers to dl_timer.o(.text.DL_TimerA_saveConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration) refers to dl_timer.o(.text.DL_TimerA_restoreConfiguration) for [Anonymous Symbol]
    dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode) refers to dl_timer.o(.text.DL_Timer_configQEIHallInputMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_init) refers to dl_uart.o(.text.DL_UART_init) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig) refers to dl_uart.o(.text.DL_UART_setClockConfig) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig) refers to dl_uart.o(.text.DL_UART_getClockConfig) for [Anonymous Symbol]
    dl_uart.o(.text.DL_UART_configBaudRate) refers to uidiv_div0.o(.text) for __aeabi_uidiv
    dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate) refers to dl_uart.o(.text.DL_UART_configBaudRate) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode) refers to dl_uart.o(.text.DL_UART_configIrDAMode) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength) refers to dl_uart.o(.text.DL_UART_setIrDAPulseLength) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking) refers to dl_uart.o(.text.DL_UART_receiveDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking) refers to dl_uart.o(.text.DL_UART_transmitDataBlocking) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck) refers to dl_uart.o(.text.DL_UART_receiveDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck) refers to dl_uart.o(.text.DL_UART_transmitDataCheck) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO) refers to dl_uart.o(.text.DL_UART_drainRXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO) refers to dl_uart.o(.text.DL_UART_fillTXFIFO) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Main_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Main_restoreConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_saveConfiguration) for [Anonymous Symbol]
    dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration) refers to dl_uart.o(.text.DL_UART_Extend_restoreConfiguration) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP) for [Anonymous Symbol]
    dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY) refers to dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY) for [Anonymous Symbol]
    asinf.o(i.__softfp_asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.__softfp_asinf) refers to asinf.o(i.asinf) for asinf
    asinf.o(i.asinf) refers (Special) to iusefp.o(.text) for __I$use$fp
    asinf.o(i.asinf) refers to fadd.o(.text) for __aeabi_frsub
    asinf.o(i.asinf) refers to fscalb.o(.text) for __ARM_scalbnf
    asinf.o(i.asinf) refers to sqrtf.o(i.sqrtf) for sqrtf
    asinf.o(i.asinf) refers to fmul.o(.text) for __aeabi_fmul
    asinf.o(i.asinf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    asinf.o(i.asinf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    asinf.o(i.asinf) refers to funder.o(i.__mathlib_flt_infnan) for __mathlib_flt_infnan
    asinf.o(i.asinf) refers to errno.o(i.__set_errno) for __set_errno
    asinf.o(i.asinf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    atan2f.o(i.__softfp_atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.__softfp_atan2f) refers to atan2f.o(i.atan2f) for atan2f
    atan2f.o(i.atan2f) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2f.o(i.atan2f) refers to fdiv.o(.text) for __aeabi_fdiv
    atan2f.o(i.atan2f) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    atan2f.o(i.atan2f) refers to errno.o(i.__set_errno) for __set_errno
    atan2f.o(i.atan2f) refers to fadd.o(.text) for __aeabi_fsub
    atan2f.o(i.atan2f) refers to fmul.o(.text) for __aeabi_fmul
    atan2f.o(i.atan2f) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.__softfp_powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.__softfp_powf) refers to powf.o(i.powf) for powf
    powf.o(i.powf) refers (Special) to iusefp.o(.text) for __I$use$fp
    powf.o(i.powf) refers to fflti.o(.text) for __aeabi_i2f
    powf.o(i.powf) refers to fscalb.o(.text) for __ARM_scalbnf
    powf.o(i.powf) refers to fadd.o(.text) for __aeabi_fadd
    powf.o(i.powf) refers to fdiv.o(.text) for __aeabi_fdiv
    powf.o(i.powf) refers to fmul.o(.text) for __aeabi_fmul
    powf.o(i.powf) refers to fpstat.o(.text) for __ieee_status
    powf.o(i.powf) refers to ffixir.o(.text) for _ffix_r
    powf.o(i.powf) refers to fpclassifyf.o(i.__ARM_fpclassifyf) for __ARM_fpclassifyf
    powf.o(i.powf) refers to funder.o(i.__mathlib_flt_underflow) for __mathlib_flt_underflow
    powf.o(i.powf) refers to errno.o(i.__set_errno) for __set_errno
    powf.o(i.powf) refers to funder.o(i.__mathlib_flt_overflow) for __mathlib_flt_overflow
    powf.o(i.powf) refers to funder.o(i.__mathlib_flt_invalid) for __mathlib_flt_invalid
    powf.o(i.powf) refers to powf.o(.constdata) for .constdata
    powf.o(i.powf) refers to funder.o(i.__mathlib_flt_infnan2) for __mathlib_flt_infnan2
    powf.o(i.powf) refers to funder.o(i.__mathlib_flt_divzero) for __mathlib_flt_divzero
    powf.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.__softfp_sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.__softfp_sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    sqrtf.o(i.sqrtf) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrtf.o(i.sqrtf) refers to fsqrt.o(.text) for _fsqrt
    sqrtf.o(i.sqrtf) refers to errno.o(i.__set_errno) for __set_errno
    roundf.o(i.roundf) refers (Special) to iusefp.o(.text) for __I$use$fp
    roundf.o(i.roundf) refers to frnd.o(.text) for _frnd
    roundf.o(i.roundf) refers to fadd.o(.text) for __aeabi_fsub
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to fputc.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to stdout.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fadd.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    fmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fdiv.o(.text) refers to fepilogue.o(.text) for _float_round
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    fcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmplt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpge.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fcmpeq.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fflti.o(.text) refers to fepilogue.o(.text) for _float_epilogue
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ffixi.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    fpclassifyf.o(i.__ARM_fpclassifyf) refers (Special) to iusefp.o(.text) for __I$use$fp
    funder.o(i.__mathlib_flt_divzero) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_infnan) refers to fscalb.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_infnan2) refers to fadd.o(.text) for __aeabi_fadd
    funder.o(i.__mathlib_flt_invalid) refers to fdiv.o(.text) for __aeabi_fdiv
    funder.o(i.__mathlib_flt_overflow) refers to fscalb.o(.text) for __ARM_scalbnf
    funder.o(i.__mathlib_flt_posinfnan) refers to fmul.o(.text) for __aeabi_fmul
    funder.o(i.__mathlib_flt_underflow) refers to fscalb.o(.text) for __ARM_scalbnf
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_mspm0g350x_uvision.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text.main) for main
    fputc.o(i.fputc) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc.o(i.fputc) refers (Special) to semi.o(.text) for __semihosting_library_function
    fputc_h.o(i._fputc$hlt) refers (Special) to iusesemip.o(.text) for __I$use$semihosting$fputc
    fputc_h.o(i._fputc$hlt) refers (Special) to semi.o(.text) for __semihosting_library_function
    idiv_div0.o(.text) refers to uidiv_div0.o(.text) for __aeabi_uidivmod
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    fepilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fscalb.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fsqrt.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    fsqrt.o(.text) refers to fepilogue.o(.text) for _float_round
    frnd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    frnd.o(.text) refers to fepilogue.o(.text) for _float_round
    depilogue.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    depilogue.o(.text) refers to depilogue.o(i.__ARM_clz) for __ARM_clz
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    ffixir.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ffixir.o(.text) refers to fepilogue.o(.text) for _float_round
    cdrcmple.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    depilogue.o(i.__ARM_clz) refers (Special) to iusefp.o(.text) for __I$use$fp


==============================================================================

Removing Unused input sections from the image.

    Removing startup_mspm0g350x_uvision.o(HEAP), (0 bytes).
    Removing ti_msp_dl_config.o(.text), (0 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_initPower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GPIO_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_LEFT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_IMU_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_GRAY_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_0_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_1_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_2_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_UART_3_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSTICK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_SYSCTL_CLK_init), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_saveConfiguration), (76 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_saveConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.text.SYSCFG_DL_restoreConfiguration), (80 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SYSCFG_DL_restoreConfiguration), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_reset), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enablePower), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralAnalogFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralOutputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunctionFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableHiZ), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initPeripheralInputFunction), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalInputFeatures), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_initDigitalOutput), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_setLowerPinsPolarity), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_GPIO_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setBORThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setFlashWaitState), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setSYSOSCFreq), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableHFXT), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_disableSYSPLL), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_setULPCLKDivider), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_enableMFCLK), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_SYSCTL_getClockStatus), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCounterControl), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_enableClock), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Timer_setCCPDirection), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setAnalogGlitchFilterPulseWidth), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableAnalogGlitchFilter), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_resetControllerTransfer), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setTimerPeriod), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerTXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_setControllerRXFIFOThreshold), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableControllerClockStretching), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_I2C_enableController), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setOversampling), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_setBaudRateDivisor), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enableInterrupt), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_UART_enable), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.SysTick_Config), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing ti_msp_dl_config.o(.ARM.exidx.text.__NVIC_SetPriority), (8 bytes).
    Removing main.o(.text), (0 bytes).
    Removing main.o(.ARM.exidx.text.main), (8 bytes).
    Removing main.o(.ARM.exidx.text.user_config), (8 bytes).
    Removing main.o(.ARM.exidx.text.__NVIC_ClearPendingIRQ), (8 bytes).
    Removing main.o(.ARM.exidx.text.__NVIC_EnableIRQ), (8 bytes).
    Removing main.o(.ARM.use_no_argv), (4 bytes).
    Removing uart_driver.o(.text), (0 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_char), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_isBusy), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_transmitData), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart_send_string), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.my_printf), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART0_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_getPendingInterrupt), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.DL_UART_receiveData), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART1_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART2_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.UART3_IRQHandler), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart0_task), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart1_task), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart2_task), (8 bytes).
    Removing uart_driver.o(.ARM.exidx.text.uart3_task), (8 bytes).
    Removing button_driver.o(.text), (0 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_read), (8 bytes).
    Removing button_driver.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing button_driver.o(.ARM.exidx.text.key_task), (8 bytes).
    Removing iic_driver.o(.text), (0 bytes).
    Removing iic_driver.o(.text.I2C_WriteByte), (114 bytes).
    Removing iic_driver.o(.ARM.exidx.text.I2C_WriteByte), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_getControllerStatus), (20 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_getControllerStatus), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_startControllerTransfer), (80 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_startControllerTransfer), (8 bytes).
    Removing iic_driver.o(.text.I2C_WriteMultiBytes), (138 bytes).
    Removing iic_driver.o(.ARM.exidx.text.I2C_WriteMultiBytes), (8 bytes).
    Removing iic_driver.o(.text.IIC_Read), (284 bytes).
    Removing iic_driver.o(.ARM.exidx.text.IIC_Read), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_transmitControllerData), (22 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_transmitControllerData), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_clearInterruptStatus), (24 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_clearInterruptStatus), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_isControllerRXFIFOEmpty), (28 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_isControllerRXFIFOEmpty), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_receiveControllerData), (20 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_receiveControllerData), (8 bytes).
    Removing iic_driver.o(.text.DL_I2C_getRawInterruptStatus), (24 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_I2C_getRawInterruptStatus), (8 bytes).
    Removing iic_driver.o(.text.BNO080_IIC_Read), (252 bytes).
    Removing iic_driver.o(.ARM.exidx.text.BNO080_IIC_Read), (8 bytes).
    Removing iic_driver.o(.text.ShortToChar), (26 bytes).
    Removing iic_driver.o(.ARM.exidx.text.ShortToChar), (8 bytes).
    Removing iic_driver.o(.text.CharToShort), (20 bytes).
    Removing iic_driver.o(.ARM.exidx.text.CharToShort), (8 bytes).
    Removing iic_driver.o(.text.iic_test), (656 bytes).
    Removing iic_driver.o(.ARM.exidx.text.iic_test), (8 bytes).
    Removing iic_driver.o(.text.DL_Common_updateReg), (40 bytes).
    Removing iic_driver.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing iic_driver.o(.bss.chrTemp), (30 bytes).
    Removing iic_driver.o(.bss.a), (12 bytes).
    Removing iic_driver.o(.bss.w), (12 bytes).
    Removing iic_driver.o(.bss.h), (12 bytes).
    Removing iic_driver.o(.bss.Angle), (12 bytes).
    Removing iic_driver.o(.rodata.str1.1), (84 bytes).
    Removing iic_driver.o(.bss.str), (100 bytes).
    Removing encoder_driver.o(.text), (0 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.GROUP1_IRQHandler), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_Interrupt_getPendingGroup), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_func), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_init), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_update), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_task), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.encoder_config), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_GPIO_getEnabledInterruptStatus), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_GPIO_readPins), (8 bytes).
    Removing encoder_driver.o(.ARM.exidx.text.DL_GPIO_clearInterruptStatus), (8 bytes).
    Removing motor_driver.o(.text), (0 bytes).
    Removing motor_driver.o(.text.Motor_Create), (126 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Create), (8 bytes).
    Removing motor_driver.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing motor_driver.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing motor_driver.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing motor_driver.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing motor_driver.o(.text.Motor_SetSpeed), (360 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_SetSpeed), (8 bytes).
    Removing motor_driver.o(.text.Motor_ValidateParams), (58 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_ValidateParams), (8 bytes).
    Removing motor_driver.o(.text.Float_To_Speed1000), (32 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Float_To_Speed1000), (8 bytes).
    Removing motor_driver.o(.text.Speed_To_PWM), (140 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Speed_To_PWM), (8 bytes).
    Removing motor_driver.o(.text.Motor_Stop), (86 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Stop), (8 bytes).
    Removing motor_driver.o(.text.Motor_GetState), (44 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_GetState), (8 bytes).
    Removing motor_driver.o(.text.Motor_Enable), (110 bytes).
    Removing motor_driver.o(.ARM.exidx.text.Motor_Enable), (8 bytes).
    Removing pid.o(.text), (0 bytes).
    Removing pid.o(.text.pid_init), (80 bytes).
    Removing pid.o(.ARM.exidx.text.pid_init), (8 bytes).
    Removing pid.o(.text.pid_set_target), (16 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_target), (8 bytes).
    Removing pid.o(.text.pid_set_params), (34 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_params), (8 bytes).
    Removing pid.o(.text.pid_set_limit), (16 bytes).
    Removing pid.o(.ARM.exidx.text.pid_set_limit), (8 bytes).
    Removing pid.o(.text.pid_reset), (38 bytes).
    Removing pid.o(.ARM.exidx.text.pid_reset), (8 bytes).
    Removing pid.o(.text.pid_calculate_positional), (34 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_positional), (8 bytes).
    Removing pid.o(.text.pid_formula_positional), (132 bytes).
    Removing pid.o(.ARM.exidx.text.pid_formula_positional), (8 bytes).
    Removing pid.o(.text.pid_out_limit), (72 bytes).
    Removing pid.o(.ARM.exidx.text.pid_out_limit), (8 bytes).
    Removing pid.o(.text.pid_calculate_incremental), (34 bytes).
    Removing pid.o(.ARM.exidx.text.pid_calculate_incremental), (8 bytes).
    Removing pid.o(.text.pid_formula_incremental), (164 bytes).
    Removing pid.o(.ARM.exidx.text.pid_formula_incremental), (8 bytes).
    Removing pid.o(.text.pid_constrain), (62 bytes).
    Removing pid.o(.ARM.exidx.text.pid_constrain), (8 bytes).
    Removing pid.o(.text.pid_app_limit_integral), (64 bytes).
    Removing pid.o(.ARM.exidx.text.pid_app_limit_integral), (8 bytes).
    Removing bno08x_hal.o(.text), (0 bytes).
    Removing bno08x_hal.o(.text.BNO080_HardwareReset), (176 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.BNO080_HardwareReset), (8 bytes).
    Removing bno08x_hal.o(.text.DL_GPIO_clearPins), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.DL_GPIO_clearPins), (8 bytes).
    Removing bno08x_hal.o(.text.DL_GPIO_setPins), (24 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.DL_GPIO_setPins), (8 bytes).
    Removing bno08x_hal.o(.text.receivePacket), (328 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.receivePacket), (8 bytes).
    Removing bno08x_hal.o(.text.BNO080_Init), (32 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.BNO080_Init), (8 bytes).
    Removing bno08x_hal.o(.text.softReset), (60 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.softReset), (8 bytes).
    Removing bno08x_hal.o(.text.sendPacket), (240 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.sendPacket), (8 bytes).
    Removing bno08x_hal.o(.text.resetReason), (76 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.resetReason), (8 bytes).
    Removing bno08x_hal.o(.text.qToFloat), (64 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.qToFloat), (8 bytes).
    Removing bno08x_hal.o(.text.dataAvailable), (72 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.dataAvailable), (8 bytes).
    Removing bno08x_hal.o(.text.parseInputReport), (628 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.parseInputReport), (8 bytes).
    Removing bno08x_hal.o(.text.getQuatI), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQuatI), (8 bytes).
    Removing bno08x_hal.o(.text.getQuatJ), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQuatJ), (8 bytes).
    Removing bno08x_hal.o(.text.getQuatK), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQuatK), (8 bytes).
    Removing bno08x_hal.o(.text.getQuatReal), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQuatReal), (8 bytes).
    Removing bno08x_hal.o(.text.getQuatRadianAccuracy), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQuatRadianAccuracy), (8 bytes).
    Removing bno08x_hal.o(.text.getQuatAccuracy), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQuatAccuracy), (8 bytes).
    Removing bno08x_hal.o(.text.getAccelX), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getAccelX), (8 bytes).
    Removing bno08x_hal.o(.text.getAccelY), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getAccelY), (8 bytes).
    Removing bno08x_hal.o(.text.getAccelZ), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getAccelZ), (8 bytes).
    Removing bno08x_hal.o(.text.getAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getAccelAccuracy), (8 bytes).
    Removing bno08x_hal.o(.text.getLinAccelX), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getLinAccelX), (8 bytes).
    Removing bno08x_hal.o(.text.getLinAccelY), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getLinAccelY), (8 bytes).
    Removing bno08x_hal.o(.text.getLinAccelZ), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getLinAccelZ), (8 bytes).
    Removing bno08x_hal.o(.text.getLinAccelAccuracy), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getLinAccelAccuracy), (8 bytes).
    Removing bno08x_hal.o(.text.getGyroX), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getGyroX), (8 bytes).
    Removing bno08x_hal.o(.text.getGyroY), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getGyroY), (8 bytes).
    Removing bno08x_hal.o(.text.getGyroZ), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getGyroZ), (8 bytes).
    Removing bno08x_hal.o(.text.getGyroAccuracy), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getGyroAccuracy), (8 bytes).
    Removing bno08x_hal.o(.text.getMagX), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getMagX), (8 bytes).
    Removing bno08x_hal.o(.text.getMagY), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getMagY), (8 bytes).
    Removing bno08x_hal.o(.text.getMagZ), (28 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getMagZ), (8 bytes).
    Removing bno08x_hal.o(.text.getMagAccuracy), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getMagAccuracy), (8 bytes).
    Removing bno08x_hal.o(.text.getStepCount), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getStepCount), (8 bytes).
    Removing bno08x_hal.o(.text.getStabilityClassifier), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getStabilityClassifier), (8 bytes).
    Removing bno08x_hal.o(.text.getActivityClassifier), (12 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getActivityClassifier), (8 bytes).
    Removing bno08x_hal.o(.text.calibrateAccelerometer), (10 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.calibrateAccelerometer), (8 bytes).
    Removing bno08x_hal.o(.text.sendCalibrateCommand), (156 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.sendCalibrateCommand), (8 bytes).
    Removing bno08x_hal.o(.text.calibrateGyro), (10 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.calibrateGyro), (8 bytes).
    Removing bno08x_hal.o(.text.calibrateMagnetometer), (10 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.calibrateMagnetometer), (8 bytes).
    Removing bno08x_hal.o(.text.calibratePlanarAccelerometer), (10 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.calibratePlanarAccelerometer), (8 bytes).
    Removing bno08x_hal.o(.text.calibrateAll), (10 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.calibrateAll), (8 bytes).
    Removing bno08x_hal.o(.text.endCalibration), (10 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.endCalibration), (8 bytes).
    Removing bno08x_hal.o(.text.saveCalibration), (60 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.saveCalibration), (8 bytes).
    Removing bno08x_hal.o(.text.sendCommand), (88 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.sendCommand), (8 bytes).
    Removing bno08x_hal.o(.text.setFeatureCommand), (104 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.setFeatureCommand), (8 bytes).
    Removing bno08x_hal.o(.text.enableRotationVector), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableRotationVector), (8 bytes).
    Removing bno08x_hal.o(.text.enableGameRotationVector), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableGameRotationVector), (8 bytes).
    Removing bno08x_hal.o(.text.enableAccelerometer), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableAccelerometer), (8 bytes).
    Removing bno08x_hal.o(.text.enableLinearAccelerometer), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableLinearAccelerometer), (8 bytes).
    Removing bno08x_hal.o(.text.enableGyro), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableGyro), (8 bytes).
    Removing bno08x_hal.o(.text.enableMagnetometer), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableMagnetometer), (8 bytes).
    Removing bno08x_hal.o(.text.enableStepCounter), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableStepCounter), (8 bytes).
    Removing bno08x_hal.o(.text.enableStabilityClassifier), (20 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.enableStabilityClassifier), (8 bytes).
    Removing bno08x_hal.o(.text.getQ1), (60 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQ1), (8 bytes).
    Removing bno08x_hal.o(.text.readFRSdata), (276 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.readFRSdata), (8 bytes).
    Removing bno08x_hal.o(.text.getQ2), (60 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQ2), (8 bytes).
    Removing bno08x_hal.o(.text.getQ3), (60 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getQ3), (8 bytes).
    Removing bno08x_hal.o(.text.getResolution), (56 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getResolution), (8 bytes).
    Removing bno08x_hal.o(.text.getRange), (56 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.getRange), (8 bytes).
    Removing bno08x_hal.o(.text.readFRSword), (56 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.readFRSword), (8 bytes).
    Removing bno08x_hal.o(.text.frsReadRequest), (80 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.frsReadRequest), (8 bytes).
    Removing bno08x_hal.o(.text.QuaternionToEulerAngles), (580 bytes).
    Removing bno08x_hal.o(.ARM.exidx.text.QuaternionToEulerAngles), (8 bytes).
    Removing bno08x_hal.o(.bss.shtpData), (128 bytes).
    Removing bno08x_hal.o(.bss.hi2c_bno080), (4 bytes).
    Removing bno08x_hal.o(.bss._deviceAddress), (1 bytes).
    Removing bno08x_hal.o(.bss.shtpHeader), (4 bytes).
    Removing bno08x_hal.o(.bss.accelAccuracy), (1 bytes).
    Removing bno08x_hal.o(.bss.rawAccelX), (2 bytes).
    Removing bno08x_hal.o(.bss.rawAccelY), (2 bytes).
    Removing bno08x_hal.o(.bss.rawAccelZ), (2 bytes).
    Removing bno08x_hal.o(.bss.accelLinAccuracy), (1 bytes).
    Removing bno08x_hal.o(.bss.rawLinAccelX), (2 bytes).
    Removing bno08x_hal.o(.bss.rawLinAccelY), (2 bytes).
    Removing bno08x_hal.o(.bss.rawLinAccelZ), (2 bytes).
    Removing bno08x_hal.o(.bss.gyroAccuracy), (1 bytes).
    Removing bno08x_hal.o(.bss.rawGyroX), (2 bytes).
    Removing bno08x_hal.o(.bss.rawGyroY), (2 bytes).
    Removing bno08x_hal.o(.bss.rawGyroZ), (2 bytes).
    Removing bno08x_hal.o(.bss.magAccuracy), (1 bytes).
    Removing bno08x_hal.o(.bss.rawMagX), (2 bytes).
    Removing bno08x_hal.o(.bss.rawMagY), (2 bytes).
    Removing bno08x_hal.o(.bss.rawMagZ), (2 bytes).
    Removing bno08x_hal.o(.bss.quatAccuracy), (1 bytes).
    Removing bno08x_hal.o(.bss.rawQuatI), (2 bytes).
    Removing bno08x_hal.o(.bss.rawQuatJ), (2 bytes).
    Removing bno08x_hal.o(.bss.rawQuatK), (2 bytes).
    Removing bno08x_hal.o(.bss.rawQuatReal), (2 bytes).
    Removing bno08x_hal.o(.bss.rawQuatRadianAccuracy), (2 bytes).
    Removing bno08x_hal.o(.bss.stepCount), (2 bytes).
    Removing bno08x_hal.o(.bss.stabilityClassifier), (1 bytes).
    Removing bno08x_hal.o(.bss.activityClassifier), (1 bytes).
    Removing bno08x_hal.o(.data._activityConfidences), (4 bytes).
    Removing bno08x_hal.o(.data.rotationVector_Q1), (4 bytes).
    Removing bno08x_hal.o(.data.accelerometer_Q1), (4 bytes).
    Removing bno08x_hal.o(.data.linear_accelerometer_Q1), (4 bytes).
    Removing bno08x_hal.o(.data.gyro_Q1), (4 bytes).
    Removing bno08x_hal.o(.data.magnetometer_Q1), (4 bytes).
    Removing bno08x_hal.o(.bss.commandSequenceNumber), (1 bytes).
    Removing bno08x_hal.o(.bss.metaData), (36 bytes).
    Removing bno08x_hal.o(.bss.activityConfidences), (10 bytes).
    Removing bno08x_hal.o(.bss.sequenceNumber), (6 bytes).
    Removing hardware_iic.o(.text), (0 bytes).
    Removing hardware_iic.o(.text.IIC_ReadByte), (28 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_ReadBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_ReadBytes), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteByte), (36 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteByte), (8 bytes).
    Removing hardware_iic.o(.text.IIC_WriteBytes), (44 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_WriteBytes), (8 bytes).
    Removing hardware_iic.o(.text.Ping), (50 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.Ping), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Digtal), (24 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Digtal), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Anolog), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Single_Anolog), (34 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Single_Anolog), (8 bytes).
    Removing hardware_iic.o(.text.IIC_Get_Normalize), (84 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.IIC_Get_Normalize), (8 bytes).
    Removing hardware_iic.o(.text.i2c_write), (52 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.i2c_write), (8 bytes).
    Removing hardware_iic.o(.text.i2c_reset), (16 bytes).
    Removing hardware_iic.o(.ARM.exidx.text.i2c_reset), (8 bytes).
    Removing hardware_iic.o(.bss.IIC_write_buff), (10 bytes).
    Removing hardware_iic.o(.data.reset_magic_number), (8 bytes).
    Removing iic.o(.text), (0 bytes).
    Removing iic.o(.text.hardware_IIC_WirteByte), (120 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_WirteByte), (8 bytes).
    Removing iic.o(.text.DL_I2C_getControllerStatus), (20 bytes).
    Removing iic.o(.ARM.exidx.text.DL_I2C_getControllerStatus), (8 bytes).
    Removing iic.o(.text.DL_I2C_startControllerTransfer), (80 bytes).
    Removing iic.o(.ARM.exidx.text.DL_I2C_startControllerTransfer), (8 bytes).
    Removing iic.o(.text.hardware_IIC_WirteBytes), (196 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_WirteBytes), (8 bytes).
    Removing iic.o(.text.hardware_IIC_ReadByte), (176 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_ReadByte), (8 bytes).
    Removing iic.o(.text.DL_I2C_receiveControllerData), (20 bytes).
    Removing iic.o(.ARM.exidx.text.DL_I2C_receiveControllerData), (8 bytes).
    Removing iic.o(.text.hardware_IIC_ReadBytes), (220 bytes).
    Removing iic.o(.ARM.exidx.text.hardware_IIC_ReadBytes), (8 bytes).
    Removing iic.o(.text.DL_Common_updateReg), (40 bytes).
    Removing iic.o(.ARM.exidx.text.DL_Common_updateReg), (8 bytes).
    Removing time.o(.text), (0 bytes).
    Removing time.o(.text.delay_us), (116 bytes).
    Removing time.o(.ARM.exidx.text.delay_us), (8 bytes).
    Removing time.o(.text.delay_ms), (22 bytes).
    Removing time.o(.ARM.exidx.text.delay_ms), (8 bytes).
    Removing scheduler.o(.text), (0 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_init), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.scheduler_run), (8 bytes).
    Removing scheduler.o(.ARM.exidx.text.SysTick_Handler), (8 bytes).
    Removing scheduler.o(.text.DL_Delay), (40 bytes).
    Removing scheduler.o(.ARM.exidx.text.DL_Delay), (8 bytes).
    Removing dl_common.o(.text), (0 bytes).
    Removing dl_common.o(.ARM.exidx.text.DL_Common_delayCycles), (8 bytes).
    Removing dl_i2c.o(.text), (0 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_setClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_getClockConfig), (26 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_getClockConfig), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillControllerTXFIFO), (44 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushControllerRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushControllerRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_fillTargetTXFIFO), (48 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_fillTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetTXFIFO), (40 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetTXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_flushTargetRXFIFO), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_flushTargetRXFIFO), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_transmitTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_transmitTargetDataCheck), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataBlocking), (20 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataBlocking), (8 bytes).
    Removing dl_i2c.o(.text.DL_I2C_receiveTargetDataCheck), (32 bytes).
    Removing dl_i2c.o(.ARM.exidx.text.DL_I2C_receiveTargetDataCheck), (8 bytes).
    Removing dl_timer.o(.text), (0 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getClockConfig), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getClockConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initTimerMode), (240 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initTimerMode), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareCtl), (40 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureMode), (300 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInput), (26 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureTriggerMode), (124 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCaptureCombinedMode), (232 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCaptureCombinedMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareMode), (168 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_initCompareTriggerMode), (112 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initCompareTriggerMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareValue), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareValue), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareCtl), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompSrcUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompSrcUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompSrcUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionDn), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionDn), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionDn), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setSecondCompActionUp), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getSecondCompActionUp), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getSecondCompActionUp), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableSuppressionOfCompEvent), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableSuppressionOfCompEvent), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptCompActUpdateMethod), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptCompActUpdateMethod), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptCompActUpdateMethod), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareOutCtl), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareOutCtl), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareAction), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareAction), (24 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareAction), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_overrideCCPOut), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_overrideCCPOut), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInput), (16 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInput), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setCaptureCompareInputFilter), (28 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getCaptureCompareInputFilter), (18 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_enableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_enableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_disableCaptureCompareInputFilter), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_disableCaptureCompareInputFilter), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_isCaptureCompareInputFilterEnabled), (20 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_isCaptureCompareInputFilterEnabled), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_saveConfiguration), (236 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_restoreConfiguration), (244 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_initFourCCPWMMode), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_setFaultSourceConfig), (44 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_setFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_getFaultSourceConfig), (32 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_getFaultSourceConfig), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_saveConfiguration), (264 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_saveConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_TimerA_restoreConfiguration), (276 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_TimerA_restoreConfiguration), (8 bytes).
    Removing dl_timer.o(.text.DL_Timer_configQEIHallInputMode), (36 bytes).
    Removing dl_timer.o(.ARM.exidx.text.DL_Timer_configQEIHallInputMode), (8 bytes).
    Removing dl_timer.o(.rodata..Lswitch.table.DL_Timer_initCompareMode), (12 bytes).
    Removing dl_uart.o(.text), (0 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_init), (8 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_getClockConfig), (16 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_getClockConfig), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configBaudRate), (140 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configBaudRate), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_configIrDAMode), (60 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_configIrDAMode), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_setIrDAPulseLength), (42 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_setIrDAPulseLength), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataBlocking), (20 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataBlocking), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_receiveDataCheck), (28 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_receiveDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_transmitDataCheck), (24 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_transmitDataCheck), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_drainRXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_drainRXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_fillTXFIFO), (40 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_fillTXFIFO), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_saveConfiguration), (88 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Main_restoreConfiguration), (120 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Main_restoreConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_saveConfiguration), (104 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_saveConfiguration), (8 bytes).
    Removing dl_uart.o(.text.DL_UART_Extend_restoreConfiguration), (132 bytes).
    Removing dl_uart.o(.ARM.exidx.text.DL_UART_Extend_restoreConfiguration), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text), (0 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configSYSPLL), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setLFCLKSourceLFXT), (80 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setLFCLKSourceLFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (60 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoLFCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromLFCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (32 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_switchMCLKfromHSCLKtoSYSOSC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXT), (76 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXT), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_setHFCLKSourceHFXTParams), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configFCC), (28 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_configFCC), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (48 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicyRUNSLEEP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTOP), (52 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTOP), (8 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getPowerPolicySTANDBY), (40 bytes).
    Removing dl_sysctl_mspm0g1x0x_g3x0x.o(.ARM.exidx.text.DL_SYSCTL_getPowerPolicySTANDBY), (8 bytes).
    Removing fadd.o(.text), (178 bytes).
    Removing fcmple.o(.text), (28 bytes).
    Removing fcmplt.o(.text), (28 bytes).
    Removing fcmpge.o(.text), (28 bytes).
    Removing fcmpeq.o(.text), (28 bytes).
    Removing dflti.o(.text), (40 bytes).
    Removing ffixi.o(.text), (50 bytes).
    Removing ffixui.o(.text), (40 bytes).
    Removing f2d.o(.text), (40 bytes).
    Removing d2f.o(.text), (56 bytes).
    Removing fscalb.o(.text), (24 bytes).
    Removing fsqrt.o(.text), (88 bytes).
    Removing frnd.o(.text), (62 bytes).
    Removing ffixir.o(.text), (76 bytes).
    Removing fpstat.o(.text), (4 bytes).

590 unused section(s) (total 17823 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast.o ABSOLUTE
    ../clib/division.s                       0x00000000   Number         0  aeabi_sdivfast_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv_div0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  idiv0.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc.o ABSOLUTE
    ../clib/microlib/stdio/fputc.c           0x00000000   Number         0  fputc_h.o ABSOLUTE
    ../clib/microlib/stdio/semi.s            0x00000000   Number         0  semi.o ABSOLUTE
    ../clib/microlib/stdio/streams.c         0x00000000   Number         0  stdout.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusesemip.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  fadd.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmple.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmplt.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpge.o ABSOLUTE
    ../fplib/microlib/fpcmp.c                0x00000000   Number         0  fcmpeq.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  ffixir.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  fflti.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  fmul.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fprnd.c                0x00000000   Number         0  frnd.o ABSOLUTE
    ../fplib/microlib/fpscalb.c              0x00000000   Number         0  fscalb.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  fsqrt.o ABSOLUTE
    ../fplib/microlib/fpstat.c               0x00000000   Number         0  fpstat.o ABSOLUTE
    ../mathlib/asinf.c                       0x00000000   Number         0  asinf.o ABSOLUTE
    ../mathlib/atan2f.c                      0x00000000   Number         0  atan2f.o ABSOLUTE
    ../mathlib/fpclassifyf.c                 0x00000000   Number         0  fpclassifyf.o ABSOLUTE
    ../mathlib/funder.c                      0x00000000   Number         0  funder.o ABSOLUTE
    ../mathlib/powf.c                        0x00000000   Number         0  powf.o ABSOLUTE
    ../mathlib/round.c                       0x00000000   Number         0  roundf.o ABSOLUTE
    ../mathlib/sqrtf.c                       0x00000000   Number         0  sqrtf.o ABSOLUTE
    IIC.c                                    0x00000000   Number         0  iic.o ABSOLUTE
    Time.c                                   0x00000000   Number         0  time.o ABSOLUTE
    bno08x_hal.c                             0x00000000   Number         0  bno08x_hal.o ABSOLUTE
    button_driver.c                          0x00000000   Number         0  button_driver.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    dl_common.c                              0x00000000   Number         0  dl_common.o ABSOLUTE
    dl_i2c.c                                 0x00000000   Number         0  dl_i2c.o ABSOLUTE
    dl_sysctl_mspm0g1x0x_g3x0x.c             0x00000000   Number         0  dl_sysctl_mspm0g1x0x_g3x0x.o ABSOLUTE
    dl_timer.c                               0x00000000   Number         0  dl_timer.o ABSOLUTE
    dl_uart.c                                0x00000000   Number         0  dl_uart.o ABSOLUTE
    encoder_driver.c                         0x00000000   Number         0  encoder_driver.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    hardware_iic.c                           0x00000000   Number         0  hardware_iic.o ABSOLUTE
    iic_driver.c                             0x00000000   Number         0  iic_driver.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    motor_driver.c                           0x00000000   Number         0  motor_driver.o ABSOLUTE
    pid.c                                    0x00000000   Number         0  pid.o ABSOLUTE
    scheduler.c                              0x00000000   Number         0  scheduler.o ABSOLUTE
    startup_mspm0g350x_uvision.s             0x00000000   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    ti_msp_dl_config.c                       0x00000000   Number         0  ti_msp_dl_config.o ABSOLUTE
    uart_driver.c                            0x00000000   Number         0  uart_driver.o ABSOLUTE
    RESET                                    0x00000000   Section      192  startup_mspm0g350x_uvision.o(RESET)
    .ARM.Collect$$$$00000000                 0x000000c0   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x000000c0   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x000000c4   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x000000c8   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x000000c8   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x000000c8   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    __lit__00000000                          0x000000d0   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .ARM.Collect$$$$0000000D                 0x000000d0   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x000000d0   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x000000d0   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x000000d4   Section       20  startup_mspm0g350x_uvision.o(.text)
    .text                                    0x000000e8   Section        0  memseta.o(.text)
    .text                                    0x0000010c   Section        0  fmul.o(.text)
    .text                                    0x00000186   Section        0  fdiv.o(.text)
    .text                                    0x00000204   Section        0  dmul.o(.text)
    .text                                    0x000002d4   Section        0  ddiv.o(.text)
    .text                                    0x000003c4   Section        0  fflti.o(.text)
    .text                                    0x000003da   Section        0  uidiv_div0.o(.text)
    .text                                    0x00000418   Section        0  uldiv.o(.text)
    .text                                    0x00000478   Section        0  iusefp.o(.text)
    .text                                    0x00000478   Section        0  fepilogue.o(.text)
    .text                                    0x000004fa   Section        0  depilogue.o(.text)
    .text                                    0x000005b8   Section        0  dadd.o(.text)
    .text                                    0x0000071c   Section        0  dfixul.o(.text)
    .text                                    0x0000075c   Section       40  cdrcmple.o(.text)
    .text                                    0x00000788   Section       48  init.o(.text)
    .text                                    0x000007b8   Section        0  llshl.o(.text)
    .text                                    0x000007d8   Section        0  llushr.o(.text)
    .text                                    0x000007fa   Section        0  llsshr.o(.text)
    [Anonymous Symbol]                       0x00000820   Section        0  dl_common.o(.text.DL_Common_delayCycles)
    DL_Common_updateReg                      0x0000082b   Thumb Code    40  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    [Anonymous Symbol]                       0x0000082a   Section        0  ti_msp_dl_config.o(.text.DL_Common_updateReg)
    DL_GPIO_clearInterruptStatus             0x00000855   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x00000854   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearInterruptStatus             0x0000086d   Thumb Code    24  encoder_driver.o(.text.DL_GPIO_clearInterruptStatus)
    [Anonymous Symbol]                       0x0000086c   Section        0  encoder_driver.o(.text.DL_GPIO_clearInterruptStatus)
    __arm_cp.9_0                             0x00000884   Number         4  encoder_driver.o(.text.DL_GPIO_clearInterruptStatus)
    DL_GPIO_clearPins                        0x00000889   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    [Anonymous Symbol]                       0x00000888   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_clearPins)
    DL_GPIO_enableHiZ                        0x0000089d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    [Anonymous Symbol]                       0x0000089c   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ)
    DL_GPIO_enableInterrupt                  0x000008b5   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    [Anonymous Symbol]                       0x000008b4   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    __arm_cp.36_0                            0x000008cc   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt)
    DL_GPIO_enableOutput                     0x000008d1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    [Anonymous Symbol]                       0x000008d0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    __arm_cp.26_0                            0x000008e4   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_enableOutput)
    DL_GPIO_enablePower                      0x000008e9   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    [Anonymous Symbol]                       0x000008e8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_enablePower)
    DL_GPIO_getEnabledInterruptStatus        0x000008fd   Thumb Code    20  encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus)
    [Anonymous Symbol]                       0x000008fc   Section        0  encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus)
    __arm_cp.7_0                             0x00000910   Number         4  encoder_driver.o(.text.DL_GPIO_getEnabledInterruptStatus)
    DL_GPIO_initDigitalInputFeatures         0x00000915   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    [Anonymous Symbol]                       0x00000914   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    __arm_cp.31_0                            0x00000940   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures)
    DL_GPIO_initDigitalOutput                0x00000945   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    [Anonymous Symbol]                       0x00000944   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput)
    DL_GPIO_initDigitalOutputFeatures        0x00000959   Thumb Code    44  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    [Anonymous Symbol]                       0x00000958   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures)
    DL_GPIO_initPeripheralAnalogFunction     0x00000985   Thumb Code    20  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    [Anonymous Symbol]                       0x00000984   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction)
    DL_GPIO_initPeripheralInputFunction      0x00000999   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    [Anonymous Symbol]                       0x00000998   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction)
    DL_GPIO_initPeripheralInputFunctionFeatures 0x000009b1   Thumb Code    52  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    [Anonymous Symbol]                       0x000009b0   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    __arm_cp.27_0                            0x000009e4   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures)
    DL_GPIO_initPeripheralOutputFunction     0x000009e9   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    [Anonymous Symbol]                       0x000009e8   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    __arm_cp.25_0                            0x00000a00   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction)
    DL_GPIO_readPins                         0x00000a05   Thumb Code    22  button_driver.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000a04   Section        0  button_driver.o(.text.DL_GPIO_readPins)
    DL_GPIO_readPins                         0x00000a1b   Thumb Code    22  encoder_driver.o(.text.DL_GPIO_readPins)
    [Anonymous Symbol]                       0x00000a1a   Section        0  encoder_driver.o(.text.DL_GPIO_readPins)
    DL_GPIO_reset                            0x00000a31   Thumb Code    16  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    [Anonymous Symbol]                       0x00000a30   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_reset)
    DL_GPIO_setLowerPinsPolarity             0x00000a41   Thumb Code    24  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    [Anonymous Symbol]                       0x00000a40   Section        0  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    __arm_cp.34_0                            0x00000a58   Number         4  ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity)
    DL_I2C_enableAnalogGlitchFilter          0x00000a5d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    [Anonymous Symbol]                       0x00000a5c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter)
    DL_I2C_enableController                  0x00000a75   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    [Anonymous Symbol]                       0x00000a74   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableController)
    DL_I2C_enableControllerClockStretching   0x00000a89   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    [Anonymous Symbol]                       0x00000a88   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    __arm_cp.54_0                            0x00000a9c   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching)
    DL_I2C_enablePower                       0x00000aa1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    [Anonymous Symbol]                       0x00000aa0   Section        0  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    __arm_cp.22_0                            0x00000ab4   Number         4  ti_msp_dl_config.o(.text.DL_I2C_enablePower)
    DL_I2C_reset                             0x00000ab9   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_reset)
    [Anonymous Symbol]                       0x00000ab8   Section        0  ti_msp_dl_config.o(.text.DL_I2C_reset)
    __arm_cp.18_0                            0x00000ac8   Number         4  ti_msp_dl_config.o(.text.DL_I2C_reset)
    __arm_cp.18_1                            0x00000acc   Number         4  ti_msp_dl_config.o(.text.DL_I2C_reset)
    DL_I2C_resetControllerTransfer           0x00000ad1   Thumb Code    16  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    [Anonymous Symbol]                       0x00000ad0   Section        0  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    __arm_cp.50_0                            0x00000ae0   Number         4  ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer)
    DL_I2C_setAnalogGlitchFilterPulseWidth   0x00000ae5   Thumb Code    38  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000ae4   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
    [Anonymous Symbol]                       0x00000b0a   Section        0  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_I2C_setControllerRXFIFOThreshold      0x00000b31   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    [Anonymous Symbol]                       0x00000b30   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold)
    DL_I2C_setControllerTXFIFOThreshold      0x00000b55   Thumb Code    36  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    [Anonymous Symbol]                       0x00000b54   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    __arm_cp.52_0                            0x00000b78   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold)
    DL_I2C_setTimerPeriod                    0x00000b7d   Thumb Code    20  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    [Anonymous Symbol]                       0x00000b7c   Section        0  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    __arm_cp.51_0                            0x00000b90   Number         4  ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod)
    DL_Interrupt_getPendingGroup             0x00000b95   Thumb Code    24  encoder_driver.o(.text.DL_Interrupt_getPendingGroup)
    [Anonymous Symbol]                       0x00000b94   Section        0  encoder_driver.o(.text.DL_Interrupt_getPendingGroup)
    __arm_cp.1_0                             0x00000bac   Number         4  encoder_driver.o(.text.DL_Interrupt_getPendingGroup)
    [Anonymous Symbol]                       0x00000bb0   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_2                             0x00000c64   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_3                             0x00000c68   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    __arm_cp.0_4                             0x00000c6c   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_disableHFXT                    0x00000c71   Thumb Code    12  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    [Anonymous Symbol]                       0x00000c70   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT)
    DL_SYSCTL_disableSYSPLL                  0x00000c7d   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    [Anonymous Symbol]                       0x00000c7c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    __arm_cp.41_0                            0x00000c8c   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL)
    DL_SYSCTL_enableMFCLK                    0x00000c91   Thumb Code    16  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    [Anonymous Symbol]                       0x00000c90   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK)
    DL_SYSCTL_getClockStatus                 0x00000ca1   Thumb Code     8  ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus)
    [Anonymous Symbol]                       0x00000ca0   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus)
    DL_SYSCTL_setBORThreshold                0x00000ca9   Thumb Code    20  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    [Anonymous Symbol]                       0x00000ca8   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    __arm_cp.37_0                            0x00000cbc   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold)
    DL_SYSCTL_setFlashWaitState              0x00000cc1   Thumb Code    28  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00000cc0   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState)
    [Anonymous Symbol]                       0x00000cdc   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    __arm_cp.7_0                             0x00000d28   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_setSYSOSCFreq                  0x00000d2d   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    [Anonymous Symbol]                       0x00000d2c   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    __arm_cp.39_0                            0x00000d44   Number         4  ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq)
    DL_SYSCTL_setULPCLKDivider               0x00000d49   Thumb Code    24  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00000d48   Section        0  ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider)
    [Anonymous Symbol]                       0x00000d60   Section        0  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_0                             0x00000d80   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    __arm_cp.4_1                             0x00000d84   Number         4  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_enableClock                     0x00000d89   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    [Anonymous Symbol]                       0x00000d88   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enableClock)
    DL_Timer_enablePower                     0x00000d99   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000d98   Section        0  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    __arm_cp.21_0                            0x00000dac   Number         4  ti_msp_dl_config.o(.text.DL_Timer_enablePower)
    [Anonymous Symbol]                       0x00000db0   Section        0  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_1                            0x00000ea4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_2                            0x00000ea8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_3                            0x00000eac   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_4                            0x00000eb0   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_5                            0x00000eb4   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    __arm_cp.40_6                            0x00000eb8   Number         4  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_reset                           0x00000ebd   Thumb Code    16  ti_msp_dl_config.o(.text.DL_Timer_reset)
    [Anonymous Symbol]                       0x00000ebc   Section        0  ti_msp_dl_config.o(.text.DL_Timer_reset)
    DL_Timer_setCCPDirection                 0x00000ecd   Thumb Code    20  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00000ecc   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection)
    [Anonymous Symbol]                       0x00000ee0   Section        0  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    __arm_cp.23_0                            0x00000ef8   Number         4  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    [Anonymous Symbol]                       0x00000efc   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    __arm_cp.27_0                            0x00000f10   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    [Anonymous Symbol]                       0x00000f14   Section        0  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    __arm_cp.3_0                             0x00000f20   Number         4  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    [Anonymous Symbol]                       0x00000f24   Section        0  dl_timer.o(.text.DL_Timer_setClockConfig)
    __arm_cp.0_0                             0x00000f3c   Number         4  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_Timer_setCounterControl               0x00000f41   Thumb Code    52  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    [Anonymous Symbol]                       0x00000f40   Section        0  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.45_0                            0x00000f74   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    __arm_cp.45_1                            0x00000f78   Number         4  ti_msp_dl_config.o(.text.DL_Timer_setCounterControl)
    DL_UART_enable                           0x00000f7d   Thumb Code    22  ti_msp_dl_config.o(.text.DL_UART_enable)
    [Anonymous Symbol]                       0x00000f7c   Section        0  ti_msp_dl_config.o(.text.DL_UART_enable)
    DL_UART_enableInterrupt                  0x00000f95   Thumb Code    24  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    [Anonymous Symbol]                       0x00000f94   Section        0  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    __arm_cp.58_0                            0x00000fac   Number         4  ti_msp_dl_config.o(.text.DL_UART_enableInterrupt)
    DL_UART_enablePower                      0x00000fb1   Thumb Code    20  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    [Anonymous Symbol]                       0x00000fb0   Section        0  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    __arm_cp.23_0                            0x00000fc4   Number         4  ti_msp_dl_config.o(.text.DL_UART_enablePower)
    DL_UART_getPendingInterrupt              0x00000fc9   Thumb Code    18  uart_driver.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00000fc8   Section        0  uart_driver.o(.text.DL_UART_getPendingInterrupt)
    [Anonymous Symbol]                       0x00000fdc   Section        0  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_0                             0x0000101c   Number         4  dl_uart.o(.text.DL_UART_init)
    __arm_cp.0_1                             0x00001020   Number         4  dl_uart.o(.text.DL_UART_init)
    DL_UART_isBusy                           0x00001025   Thumb Code    20  uart_driver.o(.text.DL_UART_isBusy)
    [Anonymous Symbol]                       0x00001024   Section        0  uart_driver.o(.text.DL_UART_isBusy)
    __arm_cp.1_0                             0x00001038   Number         4  uart_driver.o(.text.DL_UART_isBusy)
    DL_UART_receiveData                      0x0000103d   Thumb Code    16  uart_driver.o(.text.DL_UART_receiveData)
    [Anonymous Symbol]                       0x0000103c   Section        0  uart_driver.o(.text.DL_UART_receiveData)
    __arm_cp.7_0                             0x0000104c   Number         4  uart_driver.o(.text.DL_UART_receiveData)
    DL_UART_reset                            0x00001051   Thumb Code    16  ti_msp_dl_config.o(.text.DL_UART_reset)
    [Anonymous Symbol]                       0x00001050   Section        0  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.19_0                            0x00001060   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    __arm_cp.19_1                            0x00001064   Number         4  ti_msp_dl_config.o(.text.DL_UART_reset)
    DL_UART_setBaudRateDivisor               0x00001069   Thumb Code    60  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x00001068   Section        0  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.57_0                            0x000010a4   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.57_1                            0x000010a8   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.57_2                            0x000010ac   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    __arm_cp.57_3                            0x000010b0   Number         4  ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor)
    [Anonymous Symbol]                       0x000010b4   Section        0  dl_uart.o(.text.DL_UART_setClockConfig)
    DL_UART_setOversampling                  0x000010c7   Thumb Code    30  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    [Anonymous Symbol]                       0x000010c6   Section        0  ti_msp_dl_config.o(.text.DL_UART_setOversampling)
    DL_UART_transmitData                     0x000010e5   Thumb Code    22  uart_driver.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x000010e4   Section        0  uart_driver.o(.text.DL_UART_transmitData)
    [Anonymous Symbol]                       0x000010fa   Section        0  encoder_driver.o(.text.GROUP1_IRQHandler)
    [Anonymous Symbol]                       0x00001110   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_0                             0x000012bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_1                             0x000012c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    __arm_cp.2_2                             0x000012c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    [Anonymous Symbol]                       0x000012c8   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init)
    __arm_cp.7_0                             0x00001318   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init)
    __arm_cp.7_1                             0x0000131c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init)
    [Anonymous Symbol]                       0x00001320   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init)
    __arm_cp.6_0                             0x00001370   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init)
    [Anonymous Symbol]                       0x00001374   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    __arm_cp.4_0                             0x000013d4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    __arm_cp.4_2                             0x000013d8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    [Anonymous Symbol]                       0x000013dc   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    __arm_cp.5_0                             0x0000143c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    __arm_cp.5_2                             0x00001440   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    [Anonymous Symbol]                       0x00001444   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    __arm_cp.13_0                            0x0000145c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    [Anonymous Symbol]                       0x00001460   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    __arm_cp.3_0                             0x000014ac   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    [Anonymous Symbol]                       0x000014b0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    __arm_cp.12_0                            0x000014bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    [Anonymous Symbol]                       0x000014c0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_0                             0x000014fc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    __arm_cp.8_2                             0x00001500   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    [Anonymous Symbol]                       0x00001504   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.9_0                             0x00001540   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    __arm_cp.9_2                             0x00001544   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    [Anonymous Symbol]                       0x00001548   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.10_0                            0x00001584   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    __arm_cp.10_2                            0x00001588   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    [Anonymous Symbol]                       0x0000158c   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.11_0                            0x000015c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    __arm_cp.11_2                            0x000015cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    [Anonymous Symbol]                       0x000015d0   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_0                             0x00001618   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    __arm_cp.0_1                             0x0000161c   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    [Anonymous Symbol]                       0x00001620   Section        0  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_0                             0x000016bc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_1                             0x000016c0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_2                             0x000016c4   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_3                             0x000016c8   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_4                             0x000016cc   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    __arm_cp.1_5                             0x000016d0   Number         4  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Config                           0x000016d5   Thumb Code    68  ti_msp_dl_config.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x000016d4   Section        0  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.60_0                            0x00001718   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.60_1                            0x0000171c   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    __arm_cp.60_2                            0x00001720   Number         4  ti_msp_dl_config.o(.text.SysTick_Config)
    [Anonymous Symbol]                       0x00001724   Section        0  scheduler.o(.text.SysTick_Handler)
    [Anonymous Symbol]                       0x00001730   Section        0  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_0                             0x00001770   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_1                             0x00001774   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_2                             0x00001778   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    __arm_cp.5_4                             0x0000177c   Number         4  uart_driver.o(.text.UART0_IRQHandler)
    [Anonymous Symbol]                       0x00001780   Section        0  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.8_0                             0x000017c0   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.8_1                             0x000017c4   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.8_2                             0x000017c8   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    __arm_cp.8_4                             0x000017cc   Number         4  uart_driver.o(.text.UART1_IRQHandler)
    [Anonymous Symbol]                       0x000017d0   Section        0  uart_driver.o(.text.UART2_IRQHandler)
    __arm_cp.9_0                             0x00001810   Number         4  uart_driver.o(.text.UART2_IRQHandler)
    __arm_cp.9_1                             0x00001814   Number         4  uart_driver.o(.text.UART2_IRQHandler)
    __arm_cp.9_2                             0x00001818   Number         4  uart_driver.o(.text.UART2_IRQHandler)
    __arm_cp.9_4                             0x0000181c   Number         4  uart_driver.o(.text.UART2_IRQHandler)
    [Anonymous Symbol]                       0x00001820   Section        0  uart_driver.o(.text.UART3_IRQHandler)
    __arm_cp.10_0                            0x00001860   Number         4  uart_driver.o(.text.UART3_IRQHandler)
    __arm_cp.10_1                            0x00001864   Number         4  uart_driver.o(.text.UART3_IRQHandler)
    __arm_cp.10_2                            0x00001868   Number         4  uart_driver.o(.text.UART3_IRQHandler)
    __arm_cp.10_3                            0x0000186c   Number         4  uart_driver.o(.text.UART3_IRQHandler)
    __arm_cp.10_4                            0x00001870   Number         4  uart_driver.o(.text.UART3_IRQHandler)
    __NVIC_ClearPendingIRQ                   0x00001875   Thumb Code    40  main.o(.text.__NVIC_ClearPendingIRQ)
    [Anonymous Symbol]                       0x00001874   Section        0  main.o(.text.__NVIC_ClearPendingIRQ)
    __arm_cp.2_0                             0x0000189c   Number         4  main.o(.text.__NVIC_ClearPendingIRQ)
    __NVIC_EnableIRQ                         0x000018a1   Thumb Code    40  main.o(.text.__NVIC_EnableIRQ)
    [Anonymous Symbol]                       0x000018a0   Section        0  main.o(.text.__NVIC_EnableIRQ)
    __arm_cp.3_0                             0x000018c8   Number         4  main.o(.text.__NVIC_EnableIRQ)
    __NVIC_SetPriority                       0x000018cd   Thumb Code   124  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x000018cc   Section        0  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.62_0                            0x00001948   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    __arm_cp.62_1                            0x0000194c   Number         4  ti_msp_dl_config.o(.text.__NVIC_SetPriority)
    [Anonymous Symbol]                       0x00001950   Section        0  encoder_driver.o(.text.encoder_config)
    encoder_func                             0x00001971   Thumb Code   248  encoder_driver.o(.text.encoder_func)
    [Anonymous Symbol]                       0x00001970   Section        0  encoder_driver.o(.text.encoder_func)
    [Anonymous Symbol]                       0x00001a68   Section        0  encoder_driver.o(.text.encoder_init)
    [Anonymous Symbol]                       0x00001a94   Section        0  encoder_driver.o(.text.encoder_task)
    __arm_cp.5_0                             0x00001aa4   Number         4  encoder_driver.o(.text.encoder_task)
    __arm_cp.5_1                             0x00001aa8   Number         4  encoder_driver.o(.text.encoder_task)
    [Anonymous Symbol]                       0x00001aac   Section        0  encoder_driver.o(.text.encoder_update)
    __arm_cp.4_0                             0x00001b20   Number         4  encoder_driver.o(.text.encoder_update)
    __arm_cp.4_1                             0x00001b24   Number         4  encoder_driver.o(.text.encoder_update)
    __arm_cp.4_2                             0x00001b28   Number         4  encoder_driver.o(.text.encoder_update)
    __arm_cp.4_3                             0x00001b2c   Number         4  encoder_driver.o(.text.encoder_update)
    [Anonymous Symbol]                       0x00001b30   Section        0  button_driver.o(.text.key_read)
    __arm_cp.0_0                             0x00001b5c   Number         4  button_driver.o(.text.key_read)
    [Anonymous Symbol]                       0x00001b60   Section        0  button_driver.o(.text.key_task)
    __arm_cp.2_0                             0x00001b9c   Number         4  button_driver.o(.text.key_task)
    __arm_cp.2_1                             0x00001ba0   Number         4  button_driver.o(.text.key_task)
    __arm_cp.2_2                             0x00001ba4   Number         4  button_driver.o(.text.key_task)
    __arm_cp.2_3                             0x00001ba8   Number         4  button_driver.o(.text.key_task)
    __arm_cp.2_4                             0x00001bac   Number         4  button_driver.o(.text.key_task)
    [Anonymous Symbol]                       0x00001bb0   Section        0  main.o(.text.main)
    __arm_cp.0_0                             0x00001bd8   Number         4  main.o(.text.main)
    [Anonymous Symbol]                       0x00001bdc   Section        0  uart_driver.o(.text.my_printf)
    [Anonymous Symbol]                       0x00001c18   Section        0  scheduler.o(.text.scheduler_init)
    [Anonymous Symbol]                       0x00001c20   Section        0  scheduler.o(.text.scheduler_run)
    __arm_cp.1_0                             0x00001c88   Number         4  scheduler.o(.text.scheduler_run)
    __arm_cp.1_2                             0x00001c8c   Number         4  scheduler.o(.text.scheduler_run)
    [Anonymous Symbol]                       0x00001c90   Section        0  uart_driver.o(.text.uart0_task)
    __arm_cp.11_0                            0x00001cdc   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.11_2                            0x00001ce0   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.11_4                            0x00001ce4   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.11_5                            0x00001ce8   Number         4  uart_driver.o(.text.uart0_task)
    __arm_cp.11_6                            0x00001cec   Number         4  uart_driver.o(.text.uart0_task)
    [Anonymous Symbol]                       0x00001cf0   Section        0  uart_driver.o(.text.uart1_task)
    __arm_cp.12_0                            0x00001d3c   Number         4  uart_driver.o(.text.uart1_task)
    __arm_cp.12_2                            0x00001d40   Number         4  uart_driver.o(.text.uart1_task)
    __arm_cp.12_4                            0x00001d44   Number         4  uart_driver.o(.text.uart1_task)
    __arm_cp.12_5                            0x00001d48   Number         4  uart_driver.o(.text.uart1_task)
    __arm_cp.12_6                            0x00001d4c   Number         4  uart_driver.o(.text.uart1_task)
    [Anonymous Symbol]                       0x00001d50   Section        0  uart_driver.o(.text.uart2_task)
    __arm_cp.13_0                            0x00001d9c   Number         4  uart_driver.o(.text.uart2_task)
    __arm_cp.13_2                            0x00001da0   Number         4  uart_driver.o(.text.uart2_task)
    __arm_cp.13_4                            0x00001da4   Number         4  uart_driver.o(.text.uart2_task)
    __arm_cp.13_5                            0x00001da8   Number         4  uart_driver.o(.text.uart2_task)
    __arm_cp.13_6                            0x00001dac   Number         4  uart_driver.o(.text.uart2_task)
    [Anonymous Symbol]                       0x00001db0   Section        0  uart_driver.o(.text.uart3_task)
    __arm_cp.14_0                            0x00001dfc   Number         4  uart_driver.o(.text.uart3_task)
    __arm_cp.14_1                            0x00001e00   Number         4  uart_driver.o(.text.uart3_task)
    __arm_cp.14_2                            0x00001e04   Number         4  uart_driver.o(.text.uart3_task)
    __arm_cp.14_3                            0x00001e08   Number         4  uart_driver.o(.text.uart3_task)
    __arm_cp.14_4                            0x00001e0c   Number         4  uart_driver.o(.text.uart3_task)
    __arm_cp.14_5                            0x00001e10   Number         4  uart_driver.o(.text.uart3_task)
    __arm_cp.14_6                            0x00001e14   Number         4  uart_driver.o(.text.uart3_task)
    [Anonymous Symbol]                       0x00001e18   Section        0  uart_driver.o(.text.uart_send_char)
    [Anonymous Symbol]                       0x00001e40   Section        0  uart_driver.o(.text.uart_send_string)
    [Anonymous Symbol]                       0x00001e6c   Section        0  main.o(.text.user_config)
    i.__0vsnprintf                           0x00001eb4   Section        0  printfa.o(i.__0vsnprintf)
    i.__ARM_clz                              0x00001ee4   Section        0  depilogue.o(i.__ARM_clz)
    i.__scatterload_copy                     0x00001f18   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x00001f28   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x00001f30   Section       14  handlers.o(i.__scatterload_zeroinit)
    _fp_digits                               0x00001f41   Thumb Code   344  printfa.o(i._fp_digits)
    i._fp_digits                             0x00001f40   Section        0  printfa.o(i._fp_digits)
    _printf_core                             0x000020b5   Thumb Code  1754  printfa.o(i._printf_core)
    i._printf_core                           0x000020b4   Section        0  printfa.o(i._printf_core)
    _printf_post_padding                     0x000027a1   Thumb Code    32  printfa.o(i._printf_post_padding)
    i._printf_post_padding                   0x000027a0   Section        0  printfa.o(i._printf_post_padding)
    _printf_pre_padding                      0x000027c1   Thumb Code    44  printfa.o(i._printf_pre_padding)
    i._printf_pre_padding                    0x000027c0   Section        0  printfa.o(i._printf_pre_padding)
    _snputc                                  0x000027ed   Thumb Code    22  printfa.o(i._snputc)
    i._snputc                                0x000027ec   Section        0  printfa.o(i._snputc)
    gGRAYClockConfig                         0x00002802   Data           2  ti_msp_dl_config.o(.rodata.gGRAYClockConfig)
    [Anonymous Symbol]                       0x00002802   Section        0  ti_msp_dl_config.o(.rodata.gGRAYClockConfig)
    gIMUClockConfig                          0x00002804   Data           2  ti_msp_dl_config.o(.rodata.gIMUClockConfig)
    [Anonymous Symbol]                       0x00002804   Section        0  ti_msp_dl_config.o(.rodata.gIMUClockConfig)
    gMOTOR_PWM_LEFTClockConfig               0x00002806   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTClockConfig)
    [Anonymous Symbol]                       0x00002806   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTClockConfig)
    gMOTOR_PWM_LEFTConfig                    0x0000280c   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTConfig)
    [Anonymous Symbol]                       0x0000280c   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_LEFTConfig)
    gMOTOR_PWM_RIGHTClockConfig              0x00002814   Data           3  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTClockConfig)
    [Anonymous Symbol]                       0x00002814   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTClockConfig)
    gMOTOR_PWM_RIGHTConfig                   0x00002818   Data           8  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTConfig)
    [Anonymous Symbol]                       0x00002818   Section        0  ti_msp_dl_config.o(.rodata.gMOTOR_PWM_RIGHTConfig)
    gSYSPLLConfig                            0x00002820   Data          40  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    [Anonymous Symbol]                       0x00002820   Section        0  ti_msp_dl_config.o(.rodata.gSYSPLLConfig)
    gUART_0ClockConfig                       0x00002848   Data           2  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    [Anonymous Symbol]                       0x00002848   Section        0  ti_msp_dl_config.o(.rodata.gUART_0ClockConfig)
    gUART_0Config                            0x0000284a   Data          10  ti_msp_dl_config.o(.rodata.gUART_0Config)
    [Anonymous Symbol]                       0x0000284a   Section        0  ti_msp_dl_config.o(.rodata.gUART_0Config)
    gUART_1ClockConfig                       0x00002854   Data           2  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    [Anonymous Symbol]                       0x00002854   Section        0  ti_msp_dl_config.o(.rodata.gUART_1ClockConfig)
    gUART_1Config                            0x00002856   Data          10  ti_msp_dl_config.o(.rodata.gUART_1Config)
    [Anonymous Symbol]                       0x00002856   Section        0  ti_msp_dl_config.o(.rodata.gUART_1Config)
    gUART_2ClockConfig                       0x00002860   Data           2  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    [Anonymous Symbol]                       0x00002860   Section        0  ti_msp_dl_config.o(.rodata.gUART_2ClockConfig)
    gUART_2Config                            0x00002862   Data          10  ti_msp_dl_config.o(.rodata.gUART_2Config)
    [Anonymous Symbol]                       0x00002862   Section        0  ti_msp_dl_config.o(.rodata.gUART_2Config)
    gUART_3ClockConfig                       0x0000286c   Data           2  ti_msp_dl_config.o(.rodata.gUART_3ClockConfig)
    [Anonymous Symbol]                       0x0000286c   Section        0  ti_msp_dl_config.o(.rodata.gUART_3ClockConfig)
    gUART_3Config                            0x0000286e   Data          10  ti_msp_dl_config.o(.rodata.gUART_3Config)
    [Anonymous Symbol]                       0x0000286e   Section        0  ti_msp_dl_config.o(.rodata.gUART_3Config)
    [Anonymous Symbol]                       0x00002878   Section        0  main.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x0000287f   Section        0  uart_driver.o(.rodata.str1.1)
    [Anonymous Symbol]                       0x000028e8   Section        0  button_driver.o(.rodata.str1.1)
    scheduler_task                           0x20200000   Data          72  scheduler.o(.data.scheduler_task)
    [Anonymous Symbol]                       0x20200000   Section        0  scheduler.o(.data.scheduler_task)
    STACK                                    0x20200358   Section     2048  startup_mspm0g350x_uvision.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv3M$S$PE$A:L22$X:L11$S22$IEEE1$IW$~IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __Vectors                                0x00000000   Data           4  startup_mspm0g350x_uvision.o(RESET)
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_End                            0x000000c0   Data           0  startup_mspm0g350x_uvision.o(RESET)
    __Vectors_Size                           0x000000c0   Number         0  startup_mspm0g350x_uvision.o ABSOLUTE
    __main                                   0x000000c1   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x000000c1   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x000000c5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x000000c9   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x000000c9   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x000000c9   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x000000c9   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x000000d1   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x000000d1   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x000000d5   Thumb Code     4  startup_mspm0g350x_uvision.o(.text)
    NMI_Handler                              0x000000d9   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    HardFault_Handler                        0x000000db   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    SVC_Handler                              0x000000dd   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    PendSV_Handler                           0x000000df   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    ADC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    ADC1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    AES_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    CANFD0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DAC0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    DMA_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    Default_Handler                          0x000000e3   Thumb Code     2  startup_mspm0g350x_uvision.o(.text)
    GROUP0_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    I2C1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    RTC_IRQHandler                           0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI0_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    SPI1_IRQHandler                          0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMA1_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG0_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG12_IRQHandler                        0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG6_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG7_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    TIMG8_IRQHandler                         0x000000e3   Thumb Code     0  startup_mspm0g350x_uvision.o(.text)
    __aeabi_memset                           0x000000e9   Thumb Code    14  memseta.o(.text)
    __aeabi_memset4                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memset8                          0x000000e9   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr                           0x000000f7   Thumb Code     4  memseta.o(.text)
    __aeabi_memclr4                          0x000000f7   Thumb Code     0  memseta.o(.text)
    __aeabi_memclr8                          0x000000f7   Thumb Code     0  memseta.o(.text)
    _memset$wrapper                          0x000000fb   Thumb Code    18  memseta.o(.text)
    __aeabi_fmul                             0x0000010d   Thumb Code   122  fmul.o(.text)
    __aeabi_fdiv                             0x00000187   Thumb Code   124  fdiv.o(.text)
    __aeabi_dmul                             0x00000205   Thumb Code   202  dmul.o(.text)
    __aeabi_ddiv                             0x000002d5   Thumb Code   234  ddiv.o(.text)
    __aeabi_i2f                              0x000003c5   Thumb Code    22  fflti.o(.text)
    __aeabi_uidiv                            0x000003db   Thumb Code     0  uidiv_div0.o(.text)
    __aeabi_uidivmod                         0x000003db   Thumb Code    62  uidiv_div0.o(.text)
    __aeabi_uldivmod                         0x00000419   Thumb Code    96  uldiv.o(.text)
    __I$use$fp                               0x00000479   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x00000479   Thumb Code    16  fepilogue.o(.text)
    _float_epilogue                          0x00000489   Thumb Code   114  fepilogue.o(.text)
    _double_round                            0x000004fb   Thumb Code    26  depilogue.o(.text)
    _double_epilogue                         0x00000515   Thumb Code   164  depilogue.o(.text)
    __aeabi_dadd                             0x000005b9   Thumb Code   328  dadd.o(.text)
    __aeabi_dsub                             0x00000701   Thumb Code    12  dadd.o(.text)
    __aeabi_drsub                            0x0000070d   Thumb Code    12  dadd.o(.text)
    __aeabi_d2ulz                            0x0000071d   Thumb Code    54  dfixul.o(.text)
    __aeabi_cdrcmple                         0x0000075d   Thumb Code    38  cdrcmple.o(.text)
    __scatterload                            0x00000789   Thumb Code    38  init.o(.text)
    __scatterload_rt2                        0x00000789   Thumb Code     0  init.o(.text)
    __aeabi_llsl                             0x000007b9   Thumb Code    32  llshl.o(.text)
    _ll_shift_l                              0x000007b9   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x000007d9   Thumb Code    34  llushr.o(.text)
    _ll_ushift_r                             0x000007d9   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x000007fb   Thumb Code    38  llsshr.o(.text)
    _ll_sshift_r                             0x000007fb   Thumb Code     0  llsshr.o(.text)
    DL_Common_delayCycles                    0x00000821   Thumb Code    10  dl_common.o(.text.DL_Common_delayCycles)
    DL_I2C_setClockConfig                    0x00000b0b   Thumb Code    38  dl_i2c.o(.text.DL_I2C_setClockConfig)
    DL_SYSCTL_configSYSPLL                   0x00000bb1   Thumb Code   192  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL)
    DL_SYSCTL_setHFCLKSourceHFXTParams       0x00000cdd   Thumb Code    80  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams)
    DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK    0x00000d61   Thumb Code    40  dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK)
    DL_Timer_initFourCCPWMMode               0x00000db1   Thumb Code   268  dl_timer.o(.text.DL_Timer_initFourCCPWMMode)
    DL_Timer_setCaptCompUpdateMethod         0x00000ee1   Thumb Code    28  dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod)
    DL_Timer_setCaptureCompareOutCtl         0x00000efd   Thumb Code    24  dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl)
    DL_Timer_setCaptureCompareValue          0x00000f15   Thumb Code    16  dl_timer.o(.text.DL_Timer_setCaptureCompareValue)
    DL_Timer_setClockConfig                  0x00000f25   Thumb Code    28  dl_timer.o(.text.DL_Timer_setClockConfig)
    DL_UART_init                             0x00000fdd   Thumb Code    72  dl_uart.o(.text.DL_UART_init)
    DL_UART_setClockConfig                   0x000010b5   Thumb Code    18  dl_uart.o(.text.DL_UART_setClockConfig)
    GROUP1_IRQHandler                        0x000010fb   Thumb Code    22  encoder_driver.o(.text.GROUP1_IRQHandler)
    SYSCFG_DL_GPIO_init                      0x00001111   Thumb Code   428  ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init)
    SYSCFG_DL_GRAY_init                      0x000012c9   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_GRAY_init)
    SYSCFG_DL_IMU_init                       0x00001321   Thumb Code    80  ti_msp_dl_config.o(.text.SYSCFG_DL_IMU_init)
    SYSCFG_DL_MOTOR_PWM_LEFT_init            0x00001375   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_LEFT_init)
    SYSCFG_DL_MOTOR_PWM_RIGHT_init           0x000013dd   Thumb Code    96  ti_msp_dl_config.o(.text.SYSCFG_DL_MOTOR_PWM_RIGHT_init)
    SYSCFG_DL_SYSCTL_CLK_init                0x00001445   Thumb Code    24  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init)
    SYSCFG_DL_SYSCTL_init                    0x00001461   Thumb Code    76  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init)
    SYSCFG_DL_SYSTICK_init                   0x000014b1   Thumb Code    12  ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init)
    SYSCFG_DL_UART_0_init                    0x000014c1   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init)
    SYSCFG_DL_UART_1_init                    0x00001505   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init)
    SYSCFG_DL_UART_2_init                    0x00001549   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init)
    SYSCFG_DL_UART_3_init                    0x0000158d   Thumb Code    60  ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init)
    SYSCFG_DL_init                           0x000015d1   Thumb Code    72  ti_msp_dl_config.o(.text.SYSCFG_DL_init)
    SYSCFG_DL_initPower                      0x00001621   Thumb Code   156  ti_msp_dl_config.o(.text.SYSCFG_DL_initPower)
    SysTick_Handler                          0x00001725   Thumb Code    12  scheduler.o(.text.SysTick_Handler)
    UART0_IRQHandler                         0x00001731   Thumb Code    64  uart_driver.o(.text.UART0_IRQHandler)
    UART1_IRQHandler                         0x00001781   Thumb Code    64  uart_driver.o(.text.UART1_IRQHandler)
    UART2_IRQHandler                         0x000017d1   Thumb Code    64  uart_driver.o(.text.UART2_IRQHandler)
    UART3_IRQHandler                         0x00001821   Thumb Code    64  uart_driver.o(.text.UART3_IRQHandler)
    encoder_config                           0x00001951   Thumb Code    32  encoder_driver.o(.text.encoder_config)
    encoder_init                             0x00001a69   Thumb Code    42  encoder_driver.o(.text.encoder_init)
    encoder_task                             0x00001a95   Thumb Code    16  encoder_driver.o(.text.encoder_task)
    encoder_update                           0x00001aad   Thumb Code   116  encoder_driver.o(.text.encoder_update)
    key_read                                 0x00001b31   Thumb Code    44  button_driver.o(.text.key_read)
    key_task                                 0x00001b61   Thumb Code    60  button_driver.o(.text.key_task)
    main                                     0x00001bb1   Thumb Code    40  main.o(.text.main)
    my_printf                                0x00001bdd   Thumb Code    58  uart_driver.o(.text.my_printf)
    scheduler_init                           0x00001c19   Thumb Code     8  scheduler.o(.text.scheduler_init)
    scheduler_run                            0x00001c21   Thumb Code   104  scheduler.o(.text.scheduler_run)
    uart0_task                               0x00001c91   Thumb Code    76  uart_driver.o(.text.uart0_task)
    uart1_task                               0x00001cf1   Thumb Code    76  uart_driver.o(.text.uart1_task)
    uart2_task                               0x00001d51   Thumb Code    76  uart_driver.o(.text.uart2_task)
    uart3_task                               0x00001db1   Thumb Code    76  uart_driver.o(.text.uart3_task)
    uart_send_char                           0x00001e19   Thumb Code    40  uart_driver.o(.text.uart_send_char)
    uart_send_string                         0x00001e41   Thumb Code    44  uart_driver.o(.text.uart_send_string)
    user_config                              0x00001e6d   Thumb Code    70  main.o(.text.user_config)
    __0vsnprintf                             0x00001eb5   Thumb Code    44  printfa.o(i.__0vsnprintf)
    __1vsnprintf                             0x00001eb5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __2vsnprintf                             0x00001eb5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __c89vsnprintf                           0x00001eb5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    vsnprintf                                0x00001eb5   Thumb Code     0  printfa.o(i.__0vsnprintf)
    __ARM_clz                                0x00001ee5   Thumb Code    46  depilogue.o(i.__ARM_clz)
    __scatterload_copy                       0x00001f19   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x00001f29   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x00001f31   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    Region$$Table$$Base                      0x000028f8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x00002918   Number         0  anon$$obj.o(Region$$Table)
    encoder_get_count                        0x20200048   Data           4  encoder_driver.o(.bss.encoder_get_count)
    encoder_left                             0x2020004c   Data          12  encoder_driver.o(.bss.encoder_left)
    encoder_right                            0x20200058   Data          12  encoder_driver.o(.bss.encoder_right)
    gMOTOR_PWM_RIGHTBackup                   0x20200064   Data         160  ti_msp_dl_config.o(.bss.gMOTOR_PWM_RIGHTBackup)
    gUART_3Backup                            0x20200104   Data          48  ti_msp_dl_config.o(.bss.gUART_3Backup)
    key_down                                 0x20200134   Data           1  button_driver.o(.bss.key_down)
    key_old                                  0x20200135   Data           1  button_driver.o(.bss.key_old)
    key_up                                   0x20200136   Data           1  button_driver.o(.bss.key_up)
    key_val                                  0x20200137   Data           1  button_driver.o(.bss.key_val)
    task_num                                 0x20200138   Data           1  scheduler.o(.bss.task_num)
    uart_rx_buffer                           0x20200139   Data         128  uart_driver.o(.bss.uart_rx_buffer)
    uart_rx_buffer_1                         0x202001b9   Data         128  uart_driver.o(.bss.uart_rx_buffer_1)
    uart_rx_buffer_2                         0x20200239   Data         128  uart_driver.o(.bss.uart_rx_buffer_2)
    uart_rx_buffer_3                         0x202002b9   Data         128  uart_driver.o(.bss.uart_rx_buffer_3)
    uart_rx_index                            0x20200339   Data           1  uart_driver.o(.bss.uart_rx_index)
    uart_rx_index_1                          0x2020033a   Data           1  uart_driver.o(.bss.uart_rx_index_1)
    uart_rx_index_2                          0x2020033b   Data           1  uart_driver.o(.bss.uart_rx_index_2)
    uart_rx_index_3                          0x2020033c   Data           1  uart_driver.o(.bss.uart_rx_index_3)
    uart_tick                                0x20200340   Data           4  uart_driver.o(.bss.uart_tick)
    uart_tick_1                              0x20200344   Data           4  uart_driver.o(.bss.uart_tick_1)
    uart_tick_2                              0x20200348   Data           4  uart_driver.o(.bss.uart_tick_2)
    uart_tick_3                              0x2020034c   Data           4  uart_driver.o(.bss.uart_tick_3)
    uwTick                                   0x20200350   Data           4  scheduler.o(.bss.uwTick)
    __initial_sp                             0x20200b58   Data           0  startup_mspm0g350x_uvision.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x000000c1

  Load Region LR_IROM1 (Base: 0x00000000, Size: 0x00002960, Max: 0x00020000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x00000000, Load base: 0x00000000, Size: 0x00002918, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x00000000   0x00000000   0x000000c0   Data   RO            3    RESET               startup_mspm0g350x_uvision.o
    0x000000c0   0x000000c0   0x00000000   Code   RO          867  * .ARM.Collect$$$$00000000  mc_p.l(entry.o)
    0x000000c0   0x000000c0   0x00000004   Code   RO          956    .ARM.Collect$$$$00000001  mc_p.l(entry2.o)
    0x000000c4   0x000000c4   0x00000004   Code   RO          959    .ARM.Collect$$$$00000004  mc_p.l(entry5.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          961    .ARM.Collect$$$$00000008  mc_p.l(entry7b.o)
    0x000000c8   0x000000c8   0x00000000   Code   RO          963    .ARM.Collect$$$$0000000A  mc_p.l(entry8b.o)
    0x000000c8   0x000000c8   0x00000008   Code   RO          964    .ARM.Collect$$$$0000000B  mc_p.l(entry9a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          966    .ARM.Collect$$$$0000000D  mc_p.l(entry10a.o)
    0x000000d0   0x000000d0   0x00000000   Code   RO          968    .ARM.Collect$$$$0000000F  mc_p.l(entry11a.o)
    0x000000d0   0x000000d0   0x00000004   Code   RO          957    .ARM.Collect$$$$00002712  mc_p.l(entry2.o)
    0x000000d4   0x000000d4   0x00000014   Code   RO            4    .text               startup_mspm0g350x_uvision.o
    0x000000e8   0x000000e8   0x00000024   Code   RO          878    .text               mc_p.l(memseta.o)
    0x0000010c   0x0000010c   0x0000007a   Code   RO          912    .text               mf_p.l(fmul.o)
    0x00000186   0x00000186   0x0000007c   Code   RO          914    .text               mf_p.l(fdiv.o)
    0x00000202   0x00000202   0x00000002   PAD
    0x00000204   0x00000204   0x000000d0   Code   RO          916    .text               mf_p.l(dmul.o)
    0x000002d4   0x000002d4   0x000000f0   Code   RO          918    .text               mf_p.l(ddiv.o)
    0x000003c4   0x000003c4   0x00000016   Code   RO          928    .text               mf_p.l(fflti.o)
    0x000003da   0x000003da   0x0000003e   Code   RO          979    .text               mc_p.l(uidiv_div0.o)
    0x00000418   0x00000418   0x00000060   Code   RO          985    .text               mc_p.l(uldiv.o)
    0x00000478   0x00000478   0x00000000   Code   RO          994    .text               mc_p.l(iusefp.o)
    0x00000478   0x00000478   0x00000082   Code   RO          995    .text               mf_p.l(fepilogue.o)
    0x000004fa   0x000004fa   0x000000be   Code   RO         1003    .text               mf_p.l(depilogue.o)
    0x000005b8   0x000005b8   0x00000164   Code   RO         1007    .text               mf_p.l(dadd.o)
    0x0000071c   0x0000071c   0x00000040   Code   RO         1009    .text               mf_p.l(dfixul.o)
    0x0000075c   0x0000075c   0x00000028   Code   RO         1013    .text               mf_p.l(cdrcmple.o)
    0x00000784   0x00000784   0x00000004   PAD
    0x00000788   0x00000788   0x00000030   Code   RO         1017    .text               mc_p.l(init.o)
    0x000007b8   0x000007b8   0x00000020   Code   RO         1020    .text               mc_p.l(llshl.o)
    0x000007d8   0x000007d8   0x00000022   Code   RO         1022    .text               mc_p.l(llushr.o)
    0x000007fa   0x000007fa   0x00000026   Code   RO         1024    .text               mc_p.l(llsshr.o)
    0x00000820   0x00000820   0x0000000a   Code   RO          635    .text.DL_Common_delayCycles  driverlib.a(dl_common.o)
    0x0000082a   0x0000082a   0x00000028   Code   RO          133    .text.DL_Common_updateReg  ti_msp_dl_config.o
    0x00000852   0x00000852   0x00000002   PAD
    0x00000854   0x00000854   0x00000018   Code   RO           81    .text.DL_GPIO_clearInterruptStatus  ti_msp_dl_config.o
    0x0000086c   0x0000086c   0x0000001c   Code   RO          308    .text.DL_GPIO_clearInterruptStatus  encoder_driver.o
    0x00000888   0x00000888   0x00000014   Code   RO           77    .text.DL_GPIO_clearPins  ti_msp_dl_config.o
    0x0000089c   0x0000089c   0x00000018   Code   RO           67    .text.DL_GPIO_enableHiZ  ti_msp_dl_config.o
    0x000008b4   0x000008b4   0x0000001c   Code   RO           83    .text.DL_GPIO_enableInterrupt  ti_msp_dl_config.o
    0x000008d0   0x000008d0   0x00000018   Code   RO           63    .text.DL_GPIO_enableOutput  ti_msp_dl_config.o
    0x000008e8   0x000008e8   0x00000014   Code   RO           51    .text.DL_GPIO_enablePower  ti_msp_dl_config.o
    0x000008fc   0x000008fc   0x00000018   Code   RO          304    .text.DL_GPIO_getEnabledInterruptStatus  encoder_driver.o
    0x00000914   0x00000914   0x00000030   Code   RO           73    .text.DL_GPIO_initDigitalInputFeatures  ti_msp_dl_config.o
    0x00000944   0x00000944   0x00000014   Code   RO           75    .text.DL_GPIO_initDigitalOutput  ti_msp_dl_config.o
    0x00000958   0x00000958   0x0000002c   Code   RO           71    .text.DL_GPIO_initDigitalOutputFeatures  ti_msp_dl_config.o
    0x00000984   0x00000984   0x00000014   Code   RO           59    .text.DL_GPIO_initPeripheralAnalogFunction  ti_msp_dl_config.o
    0x00000998   0x00000998   0x00000018   Code   RO           69    .text.DL_GPIO_initPeripheralInputFunction  ti_msp_dl_config.o
    0x000009b0   0x000009b0   0x00000038   Code   RO           65    .text.DL_GPIO_initPeripheralInputFunctionFeatures  ti_msp_dl_config.o
    0x000009e8   0x000009e8   0x0000001c   Code   RO           61    .text.DL_GPIO_initPeripheralOutputFunction  ti_msp_dl_config.o
    0x00000a04   0x00000a04   0x00000016   Code   RO          230    .text.DL_GPIO_readPins  button_driver.o
    0x00000a1a   0x00000a1a   0x00000016   Code   RO          306    .text.DL_GPIO_readPins  encoder_driver.o
    0x00000a30   0x00000a30   0x00000010   Code   RO           43    .text.DL_GPIO_reset  ti_msp_dl_config.o
    0x00000a40   0x00000a40   0x0000001c   Code   RO           79    .text.DL_GPIO_setLowerPinsPolarity  ti_msp_dl_config.o
    0x00000a5c   0x00000a5c   0x00000018   Code   RO          109    .text.DL_I2C_enableAnalogGlitchFilter  ti_msp_dl_config.o
    0x00000a74   0x00000a74   0x00000014   Code   RO          121    .text.DL_I2C_enableController  ti_msp_dl_config.o
    0x00000a88   0x00000a88   0x00000018   Code   RO          119    .text.DL_I2C_enableControllerClockStretching  ti_msp_dl_config.o
    0x00000aa0   0x00000aa0   0x00000018   Code   RO           55    .text.DL_I2C_enablePower  ti_msp_dl_config.o
    0x00000ab8   0x00000ab8   0x00000018   Code   RO           47    .text.DL_I2C_reset  ti_msp_dl_config.o
    0x00000ad0   0x00000ad0   0x00000014   Code   RO          111    .text.DL_I2C_resetControllerTransfer  ti_msp_dl_config.o
    0x00000ae4   0x00000ae4   0x00000026   Code   RO          107    .text.DL_I2C_setAnalogGlitchFilterPulseWidth  ti_msp_dl_config.o
    0x00000b0a   0x00000b0a   0x00000026   Code   RO          644    .text.DL_I2C_setClockConfig  driverlib.a(dl_i2c.o)
    0x00000b30   0x00000b30   0x00000024   Code   RO          117    .text.DL_I2C_setControllerRXFIFOThreshold  ti_msp_dl_config.o
    0x00000b54   0x00000b54   0x00000028   Code   RO          115    .text.DL_I2C_setControllerTXFIFOThreshold  ti_msp_dl_config.o
    0x00000b7c   0x00000b7c   0x00000018   Code   RO          113    .text.DL_I2C_setTimerPeriod  ti_msp_dl_config.o
    0x00000b94   0x00000b94   0x0000001c   Code   RO          292    .text.DL_Interrupt_getPendingGroup  encoder_driver.o
    0x00000bb0   0x00000bb0   0x000000c0   Code   RO          817    .text.DL_SYSCTL_configSYSPLL  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000c70   0x00000c70   0x0000000c   Code   RO           91    .text.DL_SYSCTL_disableHFXT  ti_msp_dl_config.o
    0x00000c7c   0x00000c7c   0x00000014   Code   RO           93    .text.DL_SYSCTL_disableSYSPLL  ti_msp_dl_config.o
    0x00000c90   0x00000c90   0x00000010   Code   RO           97    .text.DL_SYSCTL_enableMFCLK  ti_msp_dl_config.o
    0x00000ca0   0x00000ca0   0x00000008   Code   RO           99    .text.DL_SYSCTL_getClockStatus  ti_msp_dl_config.o
    0x00000ca8   0x00000ca8   0x00000018   Code   RO           85    .text.DL_SYSCTL_setBORThreshold  ti_msp_dl_config.o
    0x00000cc0   0x00000cc0   0x0000001c   Code   RO           87    .text.DL_SYSCTL_setFlashWaitState  ti_msp_dl_config.o
    0x00000cdc   0x00000cdc   0x00000050   Code   RO          831    .text.DL_SYSCTL_setHFCLKSourceHFXTParams  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000d2c   0x00000d2c   0x0000001c   Code   RO           89    .text.DL_SYSCTL_setSYSOSCFreq  ti_msp_dl_config.o
    0x00000d48   0x00000d48   0x00000018   Code   RO           95    .text.DL_SYSCTL_setULPCLKDivider  ti_msp_dl_config.o
    0x00000d60   0x00000d60   0x00000028   Code   RO          825    .text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK  driverlib.a(dl_sysctl_mspm0g1x0x_g3x0x.o)
    0x00000d88   0x00000d88   0x00000010   Code   RO          103    .text.DL_Timer_enableClock  ti_msp_dl_config.o
    0x00000d98   0x00000d98   0x00000018   Code   RO           53    .text.DL_Timer_enablePower  ti_msp_dl_config.o
    0x00000db0   0x00000db0   0x0000010c   Code   RO          756    .text.DL_Timer_initFourCCPWMMode  driverlib.a(dl_timer.o)
    0x00000ebc   0x00000ebc   0x00000010   Code   RO           45    .text.DL_Timer_reset  ti_msp_dl_config.o
    0x00000ecc   0x00000ecc   0x00000014   Code   RO          105    .text.DL_Timer_setCCPDirection  ti_msp_dl_config.o
    0x00000ee0   0x00000ee0   0x0000001c   Code   RO          722    .text.DL_Timer_setCaptCompUpdateMethod  driverlib.a(dl_timer.o)
    0x00000efc   0x00000efc   0x00000018   Code   RO          730    .text.DL_Timer_setCaptureCompareOutCtl  driverlib.a(dl_timer.o)
    0x00000f14   0x00000f14   0x00000010   Code   RO          682    .text.DL_Timer_setCaptureCompareValue  driverlib.a(dl_timer.o)
    0x00000f24   0x00000f24   0x0000001c   Code   RO          676    .text.DL_Timer_setClockConfig  driverlib.a(dl_timer.o)
    0x00000f40   0x00000f40   0x0000003c   Code   RO          101    .text.DL_Timer_setCounterControl  ti_msp_dl_config.o
    0x00000f7c   0x00000f7c   0x00000016   Code   RO          129    .text.DL_UART_enable  ti_msp_dl_config.o
    0x00000f92   0x00000f92   0x00000002   PAD
    0x00000f94   0x00000f94   0x0000001c   Code   RO          127    .text.DL_UART_enableInterrupt  ti_msp_dl_config.o
    0x00000fb0   0x00000fb0   0x00000018   Code   RO           57    .text.DL_UART_enablePower  ti_msp_dl_config.o
    0x00000fc8   0x00000fc8   0x00000012   Code   RO          190    .text.DL_UART_getPendingInterrupt  uart_driver.o
    0x00000fda   0x00000fda   0x00000002   PAD
    0x00000fdc   0x00000fdc   0x00000048   Code   RO          777    .text.DL_UART_init  driverlib.a(dl_uart.o)
    0x00001024   0x00001024   0x00000018   Code   RO          180    .text.DL_UART_isBusy  uart_driver.o
    0x0000103c   0x0000103c   0x00000014   Code   RO          192    .text.DL_UART_receiveData  uart_driver.o
    0x00001050   0x00001050   0x00000018   Code   RO           49    .text.DL_UART_reset  ti_msp_dl_config.o
    0x00001068   0x00001068   0x0000004c   Code   RO          125    .text.DL_UART_setBaudRateDivisor  ti_msp_dl_config.o
    0x000010b4   0x000010b4   0x00000012   Code   RO          779    .text.DL_UART_setClockConfig  driverlib.a(dl_uart.o)
    0x000010c6   0x000010c6   0x0000001e   Code   RO          123    .text.DL_UART_setOversampling  ti_msp_dl_config.o
    0x000010e4   0x000010e4   0x00000016   Code   RO          182    .text.DL_UART_transmitData  uart_driver.o
    0x000010fa   0x000010fa   0x00000016   Code   RO          290    .text.GROUP1_IRQHandler  encoder_driver.o
    0x00001110   0x00001110   0x000001b8   Code   RO           15    .text.SYSCFG_DL_GPIO_init  ti_msp_dl_config.o
    0x000012c8   0x000012c8   0x00000058   Code   RO           25    .text.SYSCFG_DL_GRAY_init  ti_msp_dl_config.o
    0x00001320   0x00001320   0x00000054   Code   RO           23    .text.SYSCFG_DL_IMU_init  ti_msp_dl_config.o
    0x00001374   0x00001374   0x00000068   Code   RO           19    .text.SYSCFG_DL_MOTOR_PWM_LEFT_init  ti_msp_dl_config.o
    0x000013dc   0x000013dc   0x00000068   Code   RO           21    .text.SYSCFG_DL_MOTOR_PWM_RIGHT_init  ti_msp_dl_config.o
    0x00001444   0x00001444   0x0000001c   Code   RO           37    .text.SYSCFG_DL_SYSCTL_CLK_init  ti_msp_dl_config.o
    0x00001460   0x00001460   0x00000050   Code   RO           17    .text.SYSCFG_DL_SYSCTL_init  ti_msp_dl_config.o
    0x000014b0   0x000014b0   0x00000010   Code   RO           35    .text.SYSCFG_DL_SYSTICK_init  ti_msp_dl_config.o
    0x000014c0   0x000014c0   0x00000044   Code   RO           27    .text.SYSCFG_DL_UART_0_init  ti_msp_dl_config.o
    0x00001504   0x00001504   0x00000044   Code   RO           29    .text.SYSCFG_DL_UART_1_init  ti_msp_dl_config.o
    0x00001548   0x00001548   0x00000044   Code   RO           31    .text.SYSCFG_DL_UART_2_init  ti_msp_dl_config.o
    0x0000158c   0x0000158c   0x00000044   Code   RO           33    .text.SYSCFG_DL_UART_3_init  ti_msp_dl_config.o
    0x000015d0   0x000015d0   0x00000050   Code   RO           11    .text.SYSCFG_DL_init  ti_msp_dl_config.o
    0x00001620   0x00001620   0x000000b4   Code   RO           13    .text.SYSCFG_DL_initPower  ti_msp_dl_config.o
    0x000016d4   0x000016d4   0x00000050   Code   RO          131    .text.SysTick_Config  ti_msp_dl_config.o
    0x00001724   0x00001724   0x0000000c   Code   RO          621    .text.SysTick_Handler  scheduler.o
    0x00001730   0x00001730   0x00000050   Code   RO          188    .text.UART0_IRQHandler  uart_driver.o
    0x00001780   0x00001780   0x00000050   Code   RO          194    .text.UART1_IRQHandler  uart_driver.o
    0x000017d0   0x000017d0   0x00000050   Code   RO          196    .text.UART2_IRQHandler  uart_driver.o
    0x00001820   0x00001820   0x00000054   Code   RO          198    .text.UART3_IRQHandler  uart_driver.o
    0x00001874   0x00001874   0x0000002c   Code   RO          165    .text.__NVIC_ClearPendingIRQ  main.o
    0x000018a0   0x000018a0   0x0000002c   Code   RO          167    .text.__NVIC_EnableIRQ  main.o
    0x000018cc   0x000018cc   0x00000084   Code   RO          135    .text.__NVIC_SetPriority  ti_msp_dl_config.o
    0x00001950   0x00001950   0x00000020   Code   RO          302    .text.encoder_config  encoder_driver.o
    0x00001970   0x00001970   0x000000f8   Code   RO          294    .text.encoder_func  encoder_driver.o
    0x00001a68   0x00001a68   0x0000002a   Code   RO          296    .text.encoder_init  encoder_driver.o
    0x00001a92   0x00001a92   0x00000002   PAD
    0x00001a94   0x00001a94   0x00000018   Code   RO          300    .text.encoder_task  encoder_driver.o
    0x00001aac   0x00001aac   0x00000084   Code   RO          298    .text.encoder_update  encoder_driver.o
    0x00001b30   0x00001b30   0x00000030   Code   RO          228    .text.key_read      button_driver.o
    0x00001b60   0x00001b60   0x00000050   Code   RO          232    .text.key_task      button_driver.o
    0x00001bb0   0x00001bb0   0x0000002c   Code   RO          161    .text.main          main.o
    0x00001bdc   0x00001bdc   0x0000003a   Code   RO          186    .text.my_printf     uart_driver.o
    0x00001c16   0x00001c16   0x00000002   PAD
    0x00001c18   0x00001c18   0x00000008   Code   RO          617    .text.scheduler_init  scheduler.o
    0x00001c20   0x00001c20   0x00000070   Code   RO          619    .text.scheduler_run  scheduler.o
    0x00001c90   0x00001c90   0x00000060   Code   RO          200    .text.uart0_task    uart_driver.o
    0x00001cf0   0x00001cf0   0x00000060   Code   RO          202    .text.uart1_task    uart_driver.o
    0x00001d50   0x00001d50   0x00000060   Code   RO          204    .text.uart2_task    uart_driver.o
    0x00001db0   0x00001db0   0x00000068   Code   RO          206    .text.uart3_task    uart_driver.o
    0x00001e18   0x00001e18   0x00000028   Code   RO          178    .text.uart_send_char  uart_driver.o
    0x00001e40   0x00001e40   0x0000002c   Code   RO          184    .text.uart_send_string  uart_driver.o
    0x00001e6c   0x00001e6c   0x00000046   Code   RO          163    .text.user_config   main.o
    0x00001eb2   0x00001eb2   0x00000002   PAD
    0x00001eb4   0x00001eb4   0x00000030   Code   RO          888    i.__0vsnprintf      mc_p.l(printfa.o)
    0x00001ee4   0x00001ee4   0x0000002e   Code   RO         1005    i.__ARM_clz         mf_p.l(depilogue.o)
    0x00001f12   0x00001f12   0x00000006   PAD
    0x00001f18   0x00001f18   0x0000000e   Code   RO         1029    i.__scatterload_copy  mc_p.l(handlers.o)
    0x00001f26   0x00001f26   0x00000002   PAD
    0x00001f28   0x00001f28   0x00000002   Code   RO         1030    i.__scatterload_null  mc_p.l(handlers.o)
    0x00001f2a   0x00001f2a   0x00000006   PAD
    0x00001f30   0x00001f30   0x0000000e   Code   RO         1031    i.__scatterload_zeroinit  mc_p.l(handlers.o)
    0x00001f3e   0x00001f3e   0x00000002   PAD
    0x00001f40   0x00001f40   0x00000174   Code   RO          890    i._fp_digits        mc_p.l(printfa.o)
    0x000020b4   0x000020b4   0x000006ec   Code   RO          891    i._printf_core      mc_p.l(printfa.o)
    0x000027a0   0x000027a0   0x00000020   Code   RO          892    i._printf_post_padding  mc_p.l(printfa.o)
    0x000027c0   0x000027c0   0x0000002c   Code   RO          893    i._printf_pre_padding  mc_p.l(printfa.o)
    0x000027ec   0x000027ec   0x00000016   Code   RO          894    i._snputc           mc_p.l(printfa.o)
    0x00002802   0x00002802   0x00000002   Data   RO          145    .rodata.gGRAYClockConfig  ti_msp_dl_config.o
    0x00002804   0x00002804   0x00000002   Data   RO          144    .rodata.gIMUClockConfig  ti_msp_dl_config.o
    0x00002806   0x00002806   0x00000003   Data   RO          140    .rodata.gMOTOR_PWM_LEFTClockConfig  ti_msp_dl_config.o
    0x00002809   0x00002809   0x00000003   PAD
    0x0000280c   0x0000280c   0x00000008   Data   RO          141    .rodata.gMOTOR_PWM_LEFTConfig  ti_msp_dl_config.o
    0x00002814   0x00002814   0x00000003   Data   RO          142    .rodata.gMOTOR_PWM_RIGHTClockConfig  ti_msp_dl_config.o
    0x00002817   0x00002817   0x00000001   PAD
    0x00002818   0x00002818   0x00000008   Data   RO          143    .rodata.gMOTOR_PWM_RIGHTConfig  ti_msp_dl_config.o
    0x00002820   0x00002820   0x00000028   Data   RO          139    .rodata.gSYSPLLConfig  ti_msp_dl_config.o
    0x00002848   0x00002848   0x00000002   Data   RO          146    .rodata.gUART_0ClockConfig  ti_msp_dl_config.o
    0x0000284a   0x0000284a   0x0000000a   Data   RO          147    .rodata.gUART_0Config  ti_msp_dl_config.o
    0x00002854   0x00002854   0x00000002   Data   RO          148    .rodata.gUART_1ClockConfig  ti_msp_dl_config.o
    0x00002856   0x00002856   0x0000000a   Data   RO          149    .rodata.gUART_1Config  ti_msp_dl_config.o
    0x00002860   0x00002860   0x00000002   Data   RO          150    .rodata.gUART_2ClockConfig  ti_msp_dl_config.o
    0x00002862   0x00002862   0x0000000a   Data   RO          151    .rodata.gUART_2Config  ti_msp_dl_config.o
    0x0000286c   0x0000286c   0x00000002   Data   RO          152    .rodata.gUART_3ClockConfig  ti_msp_dl_config.o
    0x0000286e   0x0000286e   0x0000000a   Data   RO          153    .rodata.gUART_3Config  ti_msp_dl_config.o
    0x00002878   0x00002878   0x00000007   Data   RO          169    .rodata.str1.1      main.o
    0x0000287f   0x0000287f   0x00000069   Data   RO          220    .rodata.str1.1      uart_driver.o
    0x000028e8   0x000028e8   0x0000000f   Data   RO          238    .rodata.str1.1      button_driver.o
    0x000028f7   0x000028f7   0x00000001   PAD
    0x000028f8   0x000028f8   0x00000020   Data   RO         1028    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x20200000, Load base: 0x00002918, Size: 0x00000b58, Max: 0x00008000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20200000   0x00002918   0x00000048   Data   RW          627    .data.scheduler_task  scheduler.o
    0x20200048        -       0x00000004   Zero   RW          310    .bss.encoder_get_count  encoder_driver.o
    0x2020004c        -       0x0000000c   Zero   RW          311    .bss.encoder_left   encoder_driver.o
    0x20200058        -       0x0000000c   Zero   RW          312    .bss.encoder_right  encoder_driver.o
    0x20200064        -       0x000000a0   Zero   RW          137    .bss.gMOTOR_PWM_RIGHTBackup  ti_msp_dl_config.o
    0x20200104        -       0x00000030   Zero   RW          138    .bss.gUART_3Backup  ti_msp_dl_config.o
    0x20200134        -       0x00000001   Zero   RW          236    .bss.key_down       button_driver.o
    0x20200135        -       0x00000001   Zero   RW          235    .bss.key_old        button_driver.o
    0x20200136        -       0x00000001   Zero   RW          237    .bss.key_up         button_driver.o
    0x20200137        -       0x00000001   Zero   RW          234    .bss.key_val        button_driver.o
    0x20200138        -       0x00000001   Zero   RW          625    .bss.task_num       scheduler.o
    0x20200139        -       0x00000080   Zero   RW          208    .bss.uart_rx_buffer  uart_driver.o
    0x202001b9        -       0x00000080   Zero   RW          211    .bss.uart_rx_buffer_1  uart_driver.o
    0x20200239        -       0x00000080   Zero   RW          214    .bss.uart_rx_buffer_2  uart_driver.o
    0x202002b9        -       0x00000080   Zero   RW          217    .bss.uart_rx_buffer_3  uart_driver.o
    0x20200339        -       0x00000001   Zero   RW          209    .bss.uart_rx_index  uart_driver.o
    0x2020033a        -       0x00000001   Zero   RW          212    .bss.uart_rx_index_1  uart_driver.o
    0x2020033b        -       0x00000001   Zero   RW          215    .bss.uart_rx_index_2  uart_driver.o
    0x2020033c        -       0x00000001   Zero   RW          218    .bss.uart_rx_index_3  uart_driver.o
    0x2020033d   0x00002960   0x00000003   PAD
    0x20200340        -       0x00000004   Zero   RW          210    .bss.uart_tick      uart_driver.o
    0x20200344        -       0x00000004   Zero   RW          213    .bss.uart_tick_1    uart_driver.o
    0x20200348        -       0x00000004   Zero   RW          216    .bss.uart_tick_2    uart_driver.o
    0x2020034c        -       0x00000004   Zero   RW          219    .bss.uart_tick_3    uart_driver.o
    0x20200350        -       0x00000004   Zero   RW          626    .bss.uwTick         scheduler.o
    0x20200354   0x00002960   0x00000004   PAD
    0x20200358        -       0x00000800   Zero   RW            1    STACK               startup_mspm0g350x_uvision.o



  Load Region LR_BCR (Base: 0x41c00000, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BCR_CONFIG (Exec base: 0x41c00000, Load base: 0x41c00000, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****



  Load Region LR_BSL (Base: 0x41c00100, Size: 0x00000000, Max: 0x00000100, ABSOLUTE)

    Execution Region BSL_CONFIG (Exec base: 0x41c00100, Load base: 0x41c00100, Size: 0x00000000, Max: 0x00000080, ABSOLUTE)

    **** No section assigned to this execution region ****


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       150         24         15          0          4       5442   button_driver.o
       602         36          0          0         28       6033   encoder_driver.o
       202         12          7          0          0       4249   main.o
       132          8          0         72          5       1415   scheduler.o
        20          4        192          0       2048        628   startup_mspm0g350x_uvision.o
      2926        244        114          0        208      32090   ti_msp_dl_config.o
       942        164        105          0        532       6185   uart_driver.o

    ----------------------------------------------------------------------
      4986        <USER>        <GROUP>         72       2832      56042   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        12          0          5          0          7          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        10          0          0          0          0        803   dl_common.o
        38          0          0          0          0       8619   dl_i2c.o
       312         24          0          0          0      12876   dl_sysctl_mspm0g1x0x_g3x0x.o
       364         40          0          0          0      41556   dl_timer.o
        90          8          0          0          0      14163   dl_uart.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        48         10          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        32          0          0          0          0         68   llshl.o
        38          0          0          0          0         68   llsshr.o
        34          0          0          0          0         68   llushr.o
        36          0          0          0          0        100   memseta.o
      2290         94          0          0          0        460   printfa.o
        62          0          0          0          0         72   uidiv_div0.o
        96          0          0          0          0         84   uldiv.o
        40          2          0          0          0         68   cdrcmple.o
       356          4          0          0          0        140   dadd.o
       240          6          0          0          0         84   ddiv.o
       236          0          0          0          0        216   depilogue.o
        64         10          0          0          0         68   dfixul.o
       208          6          0          0          0         88   dmul.o
       124          0          0          0          0         72   fdiv.o
       130          0          0          0          0        144   fepilogue.o
        22          0          0          0          0         68   fflti.o
       122          0          0          0          0         72   fmul.o

    ----------------------------------------------------------------------
      5064        <USER>          <GROUP>          0          0      80025   Library Totals
        22          4          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

       814         72          0          0          0      78017   driverlib.a
      2686        112          0          0          0        988   mc_p.l
      1542         28          0          0          0       1020   mf_p.l

    ----------------------------------------------------------------------
      5064        <USER>          <GROUP>          0          0      80025   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     10050        708        470         72       2832     135043   Grand Totals
     10050        708        470         72       2832     135043   ELF Image Totals
     10050        708        470         72          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                10520 (  10.27kB)
    Total RW  Size (RW Data + ZI Data)              2904 (   2.84kB)
    Total ROM Size (Code + RO Data + RW Data)      10592 (  10.34kB)

==============================================================================

