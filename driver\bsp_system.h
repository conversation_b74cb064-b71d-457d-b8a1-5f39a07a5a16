#ifndef __BSP_SYSTEM_H
#define __BSP_SYSTEM_H

#include "stdio.h"
#include "stdarg.h"
#include "string.h"

#include "ti_msp_dl_config.h"
#include "uart_driver.h"
#include "button_driver.h"
#include "iic_driver.h"
#include "encoder_driver.h"
#include "motor_driver.h"
#include "pid.h"
#include "bno08x_hal.h"
#include "IIC.h"
#include "Time.h"
#include "hardware_iic.h"

#include "gray_app.h"

#include "scheduler.h"

extern uint32_t uwTick;    //系统时间
#endif