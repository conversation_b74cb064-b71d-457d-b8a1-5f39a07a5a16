#include "motor_app.h"

Motor_t right_motor;
Motor_t left_motor; 

void Motor_Init(void)
{
    // 右电机: TIMG7_CCP1(PWM) + GPIOA.24(AIN1) + GPIOA.26(AIN2)
    Motor_Create(&right_motor, 
                 MOTOR_PWM_RIGHT_INST, 
                 GPIO_MOTOR_PWM_RIGHT_C1_IDX,
                 MOTOR_DIR_RIGHT1_PORT, 
                 MOTOR_DIR_RIGHT1_PIN_2_PIN,
                 MOTOR_DIR_RIGHT2_PORT,
                 MOTOR_DIR_RIGHT2_PIN_3_PIN,
                 0);
    
    // 左电机: TIMG8_CCP1(PWM) + GPIOA.22(BIN1) + GPIOB.24(BIN2)  
    Motor_Create(&left_motor, 
                 MOTOR_PWM_LEFT_INST, 
                 GPIO_MOTOR_PWM_LEFT_C1_IDX,
                 MOTOR_DIR_LEFT1_PORT, 
                 MOTOR_DIR_LEFT1_PIN_0_PIN,
                 MOTOR_DIR_LEFT2_PORT,
                 MOTOR_DIR_LEFT2_PIN_1_PIN,
                 1);
}
