#include "motor_app.h"

MOTOR right_motor;
MOTOR left_motor;

void Motor_Init(void)
{
    // 右电机: TIMG7_CCP1(PWM) + GPIOA.24(AIN1) + GPIOA.26(AIN2)
    Motor_Config_Init(&right_motor,
                      MOTOR_PWM_RIGHT_INST,
                      GPIO_MOTOR_PWM_RIGHT_C1_IDX,
                      MOTOR_DIR_RIGHT1_PORT,
                      MOTOR_DIR_RIGHT1_PIN_2_PIN,
                      MOTOR_DIR_RIGHT2_PORT,
                      MOTOR_DIR_RIGHT2_PIN_3_PIN,
                      0, 50);

    // 左电机: TIMG8_CCP1(PWM) + GPIOA.22(BIN1) + GPIOB.24(BIN2)
    Motor_Config_Init(&left_motor,
                      MOTOR_PWM_LEFT_INST,
                      GPIO_MOTOR_PWM_LEFT_C1_IDX,
                      MOTOR_DIR_LEFT1_PORT,
                      MOTOR_DIR_LEFT1_PIN_0_PIN,
                      MOTOR_DIR_LEFT2_PORT,
                      MOTOR_DIR_LEFT2_PIN_1_PIN,
                      1, 50);
}

void motor_task(void)
{
    Motor_Set_Speed(&left_motor, 40);
    Motor_Set_Speed(&right_motor, 40);
}
	
