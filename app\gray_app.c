#include "gray_app.h"

unsigned char Digtal; // 开关量
float gray_weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f}; // 权重值
float g_line_position_error = 0.0f;

void Gray_Task(void)
{
    // 获取传感器开关量结果
    Digtal = ~IIC_Get_Digtal();
    my_printf(UART_0_INST, "Digtal %d-%d-%d-%d-%d-%d-%d-%d\r\n", 
              (Digtal >> 0) & 0x01, (Digtal >> 1) & 0x01, (Digtal >> 2) & 0x01, (Digtal >> 3) & 0x01, 
              (Digtal >> 4) & 0x01, (Digtal >> 5) & 0x01, (Digtal >> 6) & 0x01, (Digtal >> 7) & 0x01);
    
    unsigned char normalize_data[8];
    IIC_Get_Normalize(normalize_data, 8); // 为了下一次循环是非归一化，所以清零

    float weight_sum = 0.0f;                // 权重求和变量
    unsigned char black_line_count = 0;     // 黑线计数
    
    // 权重求和
    for (int i = 0; i < 8; i++)
    {
        if ((Digtal >> i) & 0x01)
        {
            weight_sum += gray_weights[i]; // 加权求和
            black_line_count++;            // 黑线计数
        }
    }

    // 计算循迹误差值
    if (black_line_count > 0)
        g_line_position_error = weight_sum / black_line_count;
    else
        g_line_position_error = 0;
}
