#include "pid_app.h"

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环
PID_T pid_line;        // 循迹环

int basic_speed = 40;
bool pid_running = true; // PID 控制使能开关
uint8_t pid_mode = 1;     // PID模式: 1-循迹模式

// 增量式PID：P-稳定性，I-响应性，D-准确性
// 位置式PID：P-响应性，I-准确性，D-稳定性

PidParams_t pid_params_left = {
    .kp = 0.700000f,
    .ki = 0.042000f,
    .kd = 1.800000f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_right = {
    .kp = 0.700000f,
    .ki = 0.042000f,
    .kd = 1.800000f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};

PidParams_t pid_params_line = {
    .kp = 5.0f,
    .ki = 0.0000f,
    .kd = 0.50f,
    .out_min = -999.0f,
    .out_max = 999.0f,
};

void PID_Init(void)
{
    pid_init(&pid_speed_left,
             pid_params_left.kp, pid_params_left.ki, pid_params_left.kd,
             0.0f, pid_params_left.out_max);

    pid_init(&pid_speed_right,
             pid_params_right.kp, pid_params_right.ki, pid_params_right.kd,
             0.0f, pid_params_right.out_max);

    pid_init(&pid_line,
             pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
             0.0f, pid_params_line.out_max);
             
    pid_set_target(&pid_speed_left, basic_speed);
    pid_set_target(&pid_speed_right, basic_speed);
    pid_set_target(&pid_line, 0);
}

// 循迹环控制
void pid_line_control(void)
{
    // 循迹环输出值
    int pid_line_output = 0;
    // 使用位置式PID计算循迹环输出值
    pid_line_output = pid_calculate_positional(&pid_line, g_line_position_error);
    // PID输出量限幅
    pid_line_output = pid_constrain(pid_line_output, pid_params_line.out_min, pid_params_line.out_max);
    // PID循迹环的作用处
    pid_set_target(&pid_speed_left, basic_speed - pid_line_output);
    pid_set_target(&pid_speed_right, basic_speed + pid_line_output);
}

// 低通滤波器系数
#define SPEED_FILTER_ALPHA_LEFT 0.15f
#define SPEED_FILTER_ALPHA_RIGHT 0.15f

// 用于存储滤波后速度的变量
static float filtered_speed_left = 0.0f;
static float filtered_speed_right = 0.0f;

void PID_Task(void)
{
    if (pid_running == false)
        return;

    int16_t output_left = 0, output_right = 0;

    if(pid_mode == 1)
        pid_line_control();

    // 速度环PID
    //******************************** 1.PID中的反馈 *********************************//
    // 软件滤波 得到编码器的最终得数
    filtered_speed_left = SPEED_FILTER_ALPHA_LEFT * encoder_left.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_LEFT) * filtered_speed_left;
    filtered_speed_right = SPEED_FILTER_ALPHA_RIGHT * encoder_right.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_RIGHT) * filtered_speed_right;

    //******************************** 2.PID中的计算 *********************************//
    // 计算位置式PID输出的速度环的值
    output_left = pid_calculate_positional(&pid_speed_left, filtered_speed_left);
    output_right = pid_calculate_positional(&pid_speed_right, filtered_speed_right);
    //  对PID输出的值进行限幅
    output_left = pid_constrain(output_left, pid_params_left.out_min, pid_params_left.out_max);
    output_right = pid_constrain(output_right, pid_params_right.out_min, pid_params_right.out_max);
    //******************************** 3.PID中的作用 *********************************//
    // 设置电机速度
    Motor_Set_Speed(&left_motor, 40);
    Motor_Set_Speed(&right_motor, 40);

    //******************************** 4.打印目标值和真实值 *********************************//
    my_printf(UART_0_INST, "%.2f,%.2f\r\n", pid_speed_left.target, filtered_speed_left);
}
