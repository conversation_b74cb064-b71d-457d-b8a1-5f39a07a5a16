TBD84 000:009.091   SEGGER J-Link V7.98a Log File
TBD84 000:009.290   DLL Compiled: Jul 19 2024 14:57:58
TBD84 000:009.311   Logging started @ 2025-07-30 18:21
TBD84 000:009.337   Process: D:\RYH\updedate_app\keil5\UV4\UV4.exe
TBD84 000:009.393 - 9.367ms
TBD84 000:009.433 JLINK_SetWarnOutHandler(...)
TBD84 000:009.457 - 0.026ms
TBD84 000:009.591 JLINK_OpenEx(...)
TBD84 000:011.907   Firmware: J-Link V9 compiled Dec  8 2023 20:16:22
TBD84 000:014.017   Firmware: J-Link V9 compiled Dec  8 2023 20:16:22
TBD84 000:014.334   Decompressing FW timestamp took 87 us
TBD84 000:022.615   Hardware: V9.70
TBD84 000:022.650   S/N: 602721283
TBD84 000:022.678   OEM: SEGGER
TBD84 000:022.707   Feature(s): GDB, RDI, FlashBP, FlashDL, JFlash
TBD84 000:024.709   Bootloader: (Could not read)
TBD84 000:026.906   TELNET listener socket opened on port 19021
TBD84 000:027.575   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
TBD84 000:027.693   WEBSRV Webserver running on local port 19080
TBD84 000:027.996   Looking for J-Link GUI Server exe at: D:\RYH\updedate_app\keil5\ARM\Segger\JLinkGUIServer.exe
TBD84 000:028.366   Looking for J-Link GUI Server exe at: D:\RYH\updedate_app\jlink_v720\JLink_V798a\JLinkGUIServer.exe
TBD84 000:028.525   Forking J-Link GUI Server: D:\RYH\updedate_app\jlink_v720\JLink_V798a\JLinkGUIServer.exe
TBD84 000:200.981   J-Link GUI Server info: "J-Link GUI server V7.98a "
TBD84 000:204.403 - 194.807ms returns "O.K."
TBD84 000:204.438 JLINK_GetEmuCaps()
TBD84 000:204.459 - 0.019ms returns 0xB9FF7BBF
TBD84 000:204.481 JLINK_TIF_GetAvailable(...)
TBD84 000:205.122 - 0.641ms
TBD84 000:205.153 JLINK_SetErrorOutHandler(...)
TBD84 000:205.172 - 0.019ms
TBD84 000:205.213 JLINK_ExecCommand("ProjectFile = "D:\Qian\00_dian_sai\1_ti_template(1)\ti_template\keil\JLinkSettings.ini"", ...). 
TBD84 000:216.908   Ref file found at: D:\RYH\updedate_app\keil5\ARM\Segger\JLinkDevices.ref
TBD84 000:217.256   REF file references invalid XML file: D:\RYH\updedate_app\jlink_v720\JLink_V798a\JLinkDevices.xml
TBD84 000:223.207   Device "PAC5210" selected.
TBD84 000:223.685 - 18.472ms returns 0x00
TBD84 000:225.827 JLINK_ExecCommand("Device = MSPM0G3507", ...). 
TBD84 000:227.985   Device "PAC5210" selected.
TBD84 000:228.479 - 2.623ms returns 0x00
TBD84 000:228.504 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
TBD84 000:228.527   ERROR: Unknown command
TBD84 000:228.556 - 0.030ms returns 0x01
TBD84 000:228.579 JLINK_GetHardwareVersion()
TBD84 000:228.600 - 0.021ms returns 97000
TBD84 000:228.620 JLINK_GetDLLVersion()
TBD84 000:228.640 - 0.019ms returns 79801
TBD84 000:228.660 JLINK_GetOEMString(...)
TBD84 000:228.681 JLINK_GetFirmwareString(...)
TBD84 000:228.700 - 0.019ms
TBD84 000:234.633 JLINK_GetDLLVersion()
TBD84 000:234.662 - 0.028ms returns 79801
TBD84 000:234.682 JLINK_GetCompileDateTime()
TBD84 000:234.701 - 0.019ms
TBD84 000:236.635 JLINK_GetFirmwareString(...)
TBD84 000:236.662 - 0.026ms
TBD84 000:238.627 JLINK_GetHardwareVersion()
TBD84 000:238.654 - 0.026ms returns 97000
TBD84 000:240.491 JLINK_GetSN()
TBD84 000:240.518 - 0.026ms returns 602721283
TBD84 000:242.633 JLINK_GetOEMString(...)
TBD84 000:246.497 JLINK_TIF_Select(JLINKARM_TIF_SWD)
TBD84 000:249.288 - 2.791ms returns 0x00
TBD84 000:249.316 JLINK_HasError()
TBD84 000:249.348 JLINK_SetSpeed(20000)
TBD84 000:249.914 - 0.567ms
TBD84 000:249.955 JLINK_GetId()
TBD84 000:254.440   Found SW-DP with ID 0x6BA02477
TBD84 000:261.981   DPIDR: 0x6BA02477
TBD84 000:264.016   CoreSight SoC-400 or earlier
TBD84 000:265.883   Scanning AP map to find all available APs
TBD84 000:271.928   AP[5]: Stopped AP scan as end of AP map has been reached
TBD84 000:273.706   AP[0]: AHB-AP (IDR: 0x84770001)
TBD84 000:275.965   AP[1]: MEM-AP (IDR: 0x002E0001)
TBD84 000:278.365   AP[2]: JTAG-AP (IDR: 0x002E0000)
TBD84 000:280.123   AP[3]: MEM-AP (IDR: 0x002E0003)
TBD84 000:282.228   AP[4]: MEM-AP (IDR: 0x002E0002)
TBD84 000:284.014   Iterating through AP map to find AHB-AP to use
TBD84 000:288.052   AP[0]: Core found
TBD84 000:290.211   AP[0]: AHB-AP ROM base: 0xF0000000
TBD84 000:293.160   CPUID register: 0x410CC601. Implementer code: 0x41 (ARM)
TBD84 000:295.558   Found Cortex-M0 r0p1, Little endian.
TBD84 000:296.959   -- Max. mem block: 0x00010C20
TBD84 000:298.220   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 000:299.011   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TBD84 000:299.786   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:302.511   FPUnit: 4 code (BP) slots and 0 literal slots
TBD84 000:302.549   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TBD84 000:303.296   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TBD84 000:304.041   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:304.771   CPU_WriteMem(4 bytes @ 0x********)
TBD84 000:307.425   CoreSight components:
TBD84 000:309.972   ROMTbl[0] @ F0000000
TBD84 000:310.008   CPU_ReadMem(64 bytes @ 0xF0000000)
TBD84 000:310.884   CPU_ReadMem(32 bytes @ 0xE00FFFE0)
TBD84 000:313.538   [0][0]: E00FF000 CID B105100D PID 000BB4C0 ROM Table
TBD84 000:315.464   ROMTbl[1] @ E00FF000
TBD84 000:315.501   CPU_ReadMem(64 bytes @ 0xE00FF000)
TBD84 000:316.358   CPU_ReadMem(32 bytes @ 0xE000EFE0)
TBD84 000:319.086   [1][0]: E000E000 CID B105E00D PID 000BB008 SCS
TBD84 000:319.122   CPU_ReadMem(32 bytes @ 0xE0001FE0)
TBD84 000:321.946   [1][1]: ******** CID B105E00D PID 000BB00A DWT
TBD84 000:321.982   CPU_ReadMem(32 bytes @ 0xE0002FE0)
TBD84 000:324.561   [1][2]: ******** CID B105E00D PID 000BB00B FPB
TBD84 000:324.597   CPU_ReadMem(32 bytes @ 0x40402FE0)
TBD84 000:327.690   [0][2]: 40402000 CID B105900D PID 001BB932 MTB-M0+
TBD84 000:328.400 - 78.444ms returns 0x6BA02477
TBD84 000:328.520 JLINK_GetDLLVersion()
TBD84 000:328.573 - 0.053ms returns 79801
TBD84 000:328.621 JLINK_CORE_GetFound()
TBD84 000:328.665 - 0.043ms returns 0x60000FF
TBD84 000:328.712 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TBD84 000:328.757   Value=0xF0000000
TBD84 000:328.823 - 0.111ms returns 0
TBD84 000:330.800 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
TBD84 000:330.826   Value=0xF0000000
TBD84 000:330.855 - 0.055ms returns 0
TBD84 000:330.876 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
TBD84 000:330.896   Value=0x00000000
TBD84 000:330.924 - 0.048ms returns 0
TBD84 000:330.946 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
TBD84 000:330.980   CPU_ReadMem(16 bytes @ 0xE0041FF0)
TBD84 000:331.722   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
TBD84 000:331.757 - 0.811ms returns 16 (0x10)
TBD84 000:331.779 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
TBD84 000:331.798   Value=0x40402000
TBD84 000:331.827 - 0.048ms returns 0
TBD84 000:331.847 JLINK_CORE_GetFound()
TBD84 000:331.866 - 0.019ms returns 0x60000FF
TBD84 000:331.887 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
TBD84 000:331.906   Value=0x00000000
TBD84 000:331.934 - 0.047ms returns 0
TBD84 000:331.955 JLINK_ReadMemEx(0xE0040FF0, 0x10 Bytes, Flags = 0x02000004)
TBD84 000:331.977   CPU_ReadMem(16 bytes @ 0xE0040FF0)
TBD84 000:332.688   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
TBD84 000:332.723 - 0.767ms returns 16 (0x10)
TBD84 000:332.746 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
TBD84 000:332.765   Value=0xE0000000
TBD84 000:332.794 - 0.048ms returns 0
TBD84 000:332.814 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
TBD84 000:332.833   Value=0x********
TBD84 000:332.862 - 0.047ms returns 0
TBD84 000:332.882 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
TBD84 000:332.901   Value=0x********
TBD84 000:332.930 - 0.047ms returns 0
TBD84 000:332.950 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
TBD84 000:332.969   Value=0xE000E000
TBD84 000:332.998 - 0.047ms returns 0
TBD84 000:333.018 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
TBD84 000:333.037   Value=0xE000EDF0
TBD84 000:333.066 - 0.047ms returns 0
TBD84 000:333.090 JLINK_GetDebugInfo(0x01 = Unknown)
TBD84 000:333.110   Value=0x00000000
TBD84 000:333.138 - 0.048ms returns 0
TBD84 000:333.159 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
TBD84 000:333.183   CPU_ReadMem(4 bytes @ 0xE000ED00)
TBD84 000:333.910   Data:  01 C6 0C 41
TBD84 000:333.950   Debug reg: CPUID
TBD84 000:333.979 - 0.820ms returns 1 (0x1)
TBD84 000:334.002 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
TBD84 000:334.021   Value=0x00000000
TBD84 000:334.050 - 0.048ms returns 0
TBD84 000:334.071 JLINK_HasError()
TBD84 000:334.092 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TBD84 000:334.115 - 0.023ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TBD84 000:334.136 JLINK_Reset()
TBD84 000:334.164   CPU is running
TBD84 000:334.193   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TBD84 000:334.919   CPU is running
TBD84 000:334.953   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TBD84 000:338.017   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TBD84 000:341.671   Reset: Reset device via AIRCR.SYSRESETREQ.
TBD84 000:341.707   CPU is running
TBD84 000:341.737   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TBD84 000:397.619   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 000:398.279   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 000:401.444   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TBD84 000:407.741   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 000:410.702   CPU_WriteMem(4 bytes @ 0x********)
TBD84 000:411.454   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TBD84 000:412.173   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:412.900   CPU_WriteMem(4 bytes @ 0x********)
TBD84 000:413.658 - 79.521ms
TBD84 000:413.721 JLINK_Halt()
TBD84 000:413.749 - 0.028ms returns 0x00
TBD84 000:413.769 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
TBD84 000:413.793   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 000:414.531   Data:  03 00 03 00
TBD84 000:414.565   Debug reg: DHCSR
TBD84 000:414.593 - 0.823ms returns 1 (0x1)
TBD84 000:414.621 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
TBD84 000:414.640   Debug reg: DHCSR
TBD84 000:414.847   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TBD84 000:415.551 - 0.929ms returns 0 (0x00000000)
TBD84 000:415.577 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
TBD84 000:415.597   Debug reg: DEMCR
TBD84 000:415.628   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TBD84 000:416.389 - 0.811ms returns 0 (0x00000000)
TBD84 000:424.015 JLINK_GetHWStatus(...)
TBD84 000:424.716 - 0.700ms returns 0
TBD84 000:430.148 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
TBD84 000:430.174 - 0.026ms returns 0x04
TBD84 000:430.195 JLINK_GetNumBPUnits(Type = 0xF0)
TBD84 000:430.215 - 0.020ms returns 0x2000
TBD84 000:430.236 JLINK_GetNumWPUnits()
TBD84 000:430.255 - 0.019ms returns 2
TBD84 000:437.471 JLINK_GetSpeed()
TBD84 000:437.498 - 0.027ms returns 12000
TBD84 000:441.298 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
TBD84 000:441.329   CPU_ReadMem(4 bytes @ 0xE000E004)
TBD84 000:442.049   Data:  00 00 00 00
TBD84 000:442.084 - 0.786ms returns 1 (0x1)
TBD84 000:442.110 JLINK_Halt()
TBD84 000:442.130 - 0.019ms returns 0x00
TBD84 000:442.150 JLINK_IsHalted()
TBD84 000:442.170 - 0.019ms returns TRUE
TBD84 000:445.616 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
TBD84 000:445.639   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TBD84 000:445.830   CPU_WriteMem(660 bytes @ 0x20200000)
TBD84 000:447.952 - 2.335ms returns 0x294
TBD84 000:447.994 JLINK_HasError()
TBD84 000:448.024 JLINK_WriteReg(R0, 0x00000000)
TBD84 000:448.045 - 0.028ms returns 0
TBD84 000:448.066 JLINK_WriteReg(R1, 0x01F78A40)
TBD84 000:448.085 - 0.019ms returns 0
TBD84 000:448.106 JLINK_WriteReg(R2, 0x00000001)
TBD84 000:448.125 - 0.019ms returns 0
TBD84 000:448.145 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:448.165 - 0.019ms returns 0
TBD84 000:448.185 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:448.205 - 0.019ms returns 0
TBD84 000:448.225 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:448.244 - 0.019ms returns 0
TBD84 000:448.265 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:448.284 - 0.019ms returns 0
TBD84 000:448.304 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:448.324 - 0.019ms returns 0
TBD84 000:448.348 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:448.367 - 0.023ms returns 0
TBD84 000:448.388 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:448.410 - 0.022ms returns 0
TBD84 000:448.430 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:448.450 - 0.019ms returns 0
TBD84 000:448.470 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:448.490 - 0.019ms returns 0
TBD84 000:448.510 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:448.529 - 0.019ms returns 0
TBD84 000:448.550 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:448.570 - 0.020ms returns 0
TBD84 000:448.590 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:448.615 - 0.024ms returns 0
TBD84 000:448.639 JLINK_WriteReg(R15 (PC), 0x20200038)
TBD84 000:448.659 - 0.023ms returns 0
TBD84 000:448.679 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:448.699 - 0.019ms returns 0
TBD84 000:448.719 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:448.738 - 0.019ms returns 0
TBD84 000:448.759 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:448.778 - 0.019ms returns 0
TBD84 000:448.799 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:448.818 - 0.019ms returns 0
TBD84 000:448.839 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:448.861   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 000:449.563   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 000:450.316   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 000:451.065   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 000:451.794   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:452.524 - 3.685ms returns 0x00000001
TBD84 000:452.550 JLINK_Go()
TBD84 000:452.572   CPU_WriteMem(2 bytes @ 0x20200000)
TBD84 000:453.300   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:454.034   CPU_WriteMem(4 bytes @ 0x********)
TBD84 000:454.795   CPU_WriteMem(4 bytes @ 0xE0002008)
TBD84 000:454.830   CPU_WriteMem(4 bytes @ 0xE000200C)
TBD84 000:454.858   CPU_WriteMem(4 bytes @ 0xE0002010)
TBD84 000:454.892   CPU_WriteMem(4 bytes @ 0xE0002014)
TBD84 000:455.896   CPU_WriteMem(4 bytes @ 0xE0001004)
TBD84 000:461.860   Memory map 'after startup completion point' is active
TBD84 000:461.895 - 9.345ms
TBD84 000:461.918 JLINK_IsHalted()
TBD84 000:464.192   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:464.907 - 2.988ms returns TRUE
TBD84 000:464.937 JLINK_ReadReg(R15 (PC))
TBD84 000:464.958 - 0.021ms returns 0x20200000
TBD84 000:464.981 JLINK_ClrBPEx(BPHandle = 0x00000001)
TBD84 000:465.001 - 0.020ms returns 0x00
TBD84 000:465.024 JLINK_ReadReg(R0)
TBD84 000:465.044 - 0.019ms returns 0x00000000
TBD84 000:465.287 JLINK_HasError()
TBD84 000:465.312 JLINK_WriteReg(R0, 0x00000000)
TBD84 000:465.332 - 0.020ms returns 0
TBD84 000:465.357 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:465.377 - 0.019ms returns 0
TBD84 000:465.397 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:465.417 - 0.019ms returns 0
TBD84 000:465.437 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:465.457 - 0.019ms returns 0
TBD84 000:465.477 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:465.497 - 0.019ms returns 0
TBD84 000:465.517 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:465.536 - 0.019ms returns 0
TBD84 000:465.557 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:465.576 - 0.019ms returns 0
TBD84 000:465.597 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:465.616 - 0.019ms returns 0
TBD84 000:465.637 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:465.656 - 0.019ms returns 0
TBD84 000:465.676 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:465.696 - 0.019ms returns 0
TBD84 000:465.716 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:465.736 - 0.019ms returns 0
TBD84 000:465.762 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:465.782 - 0.019ms returns 0
TBD84 000:465.810 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:465.838 - 0.028ms returns 0
TBD84 000:465.862 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:465.882 - 0.019ms returns 0
TBD84 000:465.902 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:465.922 - 0.019ms returns 0
TBD84 000:465.943 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:465.963 - 0.020ms returns 0
TBD84 000:465.984 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:466.003 - 0.019ms returns 0
TBD84 000:466.023 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:466.043 - 0.019ms returns 0
TBD84 000:466.063 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:466.083 - 0.019ms returns 0
TBD84 000:466.143 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:466.163 - 0.019ms returns 0
TBD84 000:466.184 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:466.204 - 0.020ms returns 0x00000002
TBD84 000:466.224 JLINK_Go()
TBD84 000:466.247   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:469.083 - 2.858ms
TBD84 000:469.110 JLINK_IsHalted()
TBD84 000:471.334   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:472.025 - 2.914ms returns TRUE
TBD84 000:472.052 JLINK_ReadReg(R15 (PC))
TBD84 000:472.073 - 0.020ms returns 0x20200000
TBD84 000:472.094 JLINK_ClrBPEx(BPHandle = 0x00000002)
TBD84 000:472.113 - 0.019ms returns 0x00
TBD84 000:472.134 JLINK_ReadReg(R0)
TBD84 000:472.153 - 0.019ms returns 0x00000001
TBD84 000:472.174 JLINK_HasError()
TBD84 000:472.194 JLINK_WriteReg(R0, 0x00000000)
TBD84 000:472.214 - 0.019ms returns 0
TBD84 000:472.234 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:472.254 - 0.019ms returns 0
TBD84 000:472.274 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:472.293 - 0.019ms returns 0
TBD84 000:472.319 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:472.338 - 0.019ms returns 0
TBD84 000:472.359 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:472.382 - 0.023ms returns 0
TBD84 000:472.402 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:472.422 - 0.019ms returns 0
TBD84 000:472.447 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:472.466 - 0.019ms returns 0
TBD84 000:472.486 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:472.505 - 0.019ms returns 0
TBD84 000:472.525 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:472.545 - 0.019ms returns 0
TBD84 000:472.565 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:472.584 - 0.019ms returns 0
TBD84 000:472.604 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:472.624 - 0.019ms returns 0
TBD84 000:472.644 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:472.663 - 0.019ms returns 0
TBD84 000:472.683 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:472.702 - 0.019ms returns 0
TBD84 000:472.723 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:472.742 - 0.019ms returns 0
TBD84 000:472.762 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:472.781 - 0.019ms returns 0
TBD84 000:472.802 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:472.821 - 0.019ms returns 0
TBD84 000:472.841 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:472.860 - 0.019ms returns 0
TBD84 000:472.880 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:472.900 - 0.019ms returns 0
TBD84 000:472.920 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:472.939 - 0.019ms returns 0
TBD84 000:472.960 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:472.979 - 0.019ms returns 0
TBD84 000:472.999 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:473.019 - 0.019ms returns 0x00000003
TBD84 000:473.039 JLINK_Go()
TBD84 000:473.060   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:476.074 - 3.035ms
TBD84 000:476.102 JLINK_IsHalted()
TBD84 000:476.784 - 0.681ms returns FALSE
TBD84 000:476.811 JLINK_HasError()
TBD84 000:487.325 JLINK_IsHalted()
TBD84 000:489.681   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:490.414 - 3.089ms returns TRUE
TBD84 000:490.444 JLINK_ReadReg(R15 (PC))
TBD84 000:490.466 - 0.021ms returns 0x20200000
TBD84 000:490.488 JLINK_ClrBPEx(BPHandle = 0x00000003)
TBD84 000:490.508 - 0.019ms returns 0x00
TBD84 000:490.530 JLINK_ReadReg(R0)
TBD84 000:490.550 - 0.019ms returns 0x00000000
TBD84 000:491.178 JLINK_HasError()
TBD84 000:491.206 JLINK_WriteReg(R0, 0x00000400)
TBD84 000:491.226 - 0.020ms returns 0
TBD84 000:491.247 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:491.267 - 0.019ms returns 0
TBD84 000:491.287 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:491.306 - 0.019ms returns 0
TBD84 000:491.327 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:491.346 - 0.019ms returns 0
TBD84 000:491.367 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:491.390 - 0.023ms returns 0
TBD84 000:491.411 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:491.430 - 0.019ms returns 0
TBD84 000:491.451 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:491.470 - 0.019ms returns 0
TBD84 000:491.490 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:491.510 - 0.019ms returns 0
TBD84 000:491.532 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:491.553 - 0.020ms returns 0
TBD84 000:491.574 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:491.593 - 0.019ms returns 0
TBD84 000:491.614 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:491.633 - 0.019ms returns 0
TBD84 000:491.653 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:491.673 - 0.019ms returns 0
TBD84 000:491.694 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:491.713 - 0.019ms returns 0
TBD84 000:491.734 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:491.753 - 0.019ms returns 0
TBD84 000:491.774 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:491.793 - 0.019ms returns 0
TBD84 000:491.814 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:491.839 - 0.025ms returns 0
TBD84 000:491.859 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:491.879 - 0.019ms returns 0
TBD84 000:491.900 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:491.919 - 0.019ms returns 0
TBD84 000:491.940 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:491.959 - 0.019ms returns 0
TBD84 000:491.980 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:491.999 - 0.019ms returns 0
TBD84 000:492.020 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:492.040 - 0.020ms returns 0x00000004
TBD84 000:492.061 JLINK_Go()
TBD84 000:492.083   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:495.081 - 3.019ms
TBD84 000:495.109 JLINK_IsHalted()
TBD84 000:497.336   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:498.032 - 2.923ms returns TRUE
TBD84 000:498.060 JLINK_ReadReg(R15 (PC))
TBD84 000:498.080 - 0.020ms returns 0x20200000
TBD84 000:498.101 JLINK_ClrBPEx(BPHandle = 0x00000004)
TBD84 000:498.121 - 0.019ms returns 0x00
TBD84 000:498.141 JLINK_ReadReg(R0)
TBD84 000:498.160 - 0.019ms returns 0x00000001
TBD84 000:498.181 JLINK_HasError()
TBD84 000:498.202 JLINK_WriteReg(R0, 0x00000400)
TBD84 000:498.221 - 0.019ms returns 0
TBD84 000:498.242 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:498.261 - 0.019ms returns 0
TBD84 000:498.282 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:498.301 - 0.019ms returns 0
TBD84 000:498.321 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:498.340 - 0.019ms returns 0
TBD84 000:498.361 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:498.380 - 0.019ms returns 0
TBD84 000:498.400 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:498.424 - 0.023ms returns 0
TBD84 000:498.444 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:498.463 - 0.019ms returns 0
TBD84 000:498.483 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:498.502 - 0.019ms returns 0
TBD84 000:498.522 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:498.541 - 0.019ms returns 0
TBD84 000:498.561 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:498.580 - 0.019ms returns 0
TBD84 000:498.600 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:498.619 - 0.019ms returns 0
TBD84 000:498.639 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:498.658 - 0.019ms returns 0
TBD84 000:498.679 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:498.698 - 0.019ms returns 0
TBD84 000:498.718 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:498.740 - 0.022ms returns 0
TBD84 000:498.762 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:498.781 - 0.019ms returns 0
TBD84 000:498.801 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:498.825 - 0.023ms returns 0
TBD84 000:498.845 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:498.864 - 0.019ms returns 0
TBD84 000:498.885 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:498.904 - 0.019ms returns 0
TBD84 000:498.924 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:498.943 - 0.019ms returns 0
TBD84 000:498.963 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:498.982 - 0.019ms returns 0
TBD84 000:499.003 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:499.022 - 0.019ms returns 0x00000005
TBD84 000:499.042 JLINK_Go()
TBD84 000:499.064   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:501.948 - 2.905ms
TBD84 000:502.011 JLINK_IsHalted()
TBD84 000:502.771 - 0.759ms returns FALSE
TBD84 000:502.798 JLINK_HasError()
TBD84 000:504.662 JLINK_IsHalted()
TBD84 000:506.956   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:507.678 - 3.015ms returns TRUE
TBD84 000:507.708 JLINK_ReadReg(R15 (PC))
TBD84 000:507.733 - 0.024ms returns 0x20200000
TBD84 000:507.756 JLINK_ClrBPEx(BPHandle = 0x00000005)
TBD84 000:507.778 - 0.021ms returns 0x00
TBD84 000:507.800 JLINK_ReadReg(R0)
TBD84 000:507.821 - 0.021ms returns 0x00000000
TBD84 000:508.299 JLINK_HasError()
TBD84 000:508.328 JLINK_WriteReg(R0, 0x00000800)
TBD84 000:508.350 - 0.022ms returns 0
TBD84 000:508.373 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:508.394 - 0.021ms returns 0
TBD84 000:508.417 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:508.439 - 0.021ms returns 0
TBD84 000:508.464 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:508.486 - 0.021ms returns 0
TBD84 000:508.509 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:508.530 - 0.021ms returns 0
TBD84 000:508.552 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:508.573 - 0.021ms returns 0
TBD84 000:508.596 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:508.618 - 0.022ms returns 0
TBD84 000:508.641 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:508.662 - 0.021ms returns 0
TBD84 000:508.684 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:508.706 - 0.021ms returns 0
TBD84 000:508.728 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:508.749 - 0.021ms returns 0
TBD84 000:508.771 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:508.793 - 0.021ms returns 0
TBD84 000:508.815 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:508.837 - 0.021ms returns 0
TBD84 000:508.859 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:508.880 - 0.021ms returns 0
TBD84 000:508.903 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:508.924 - 0.021ms returns 0
TBD84 000:508.946 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:508.968 - 0.021ms returns 0
TBD84 000:508.990 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:509.012 - 0.021ms returns 0
TBD84 000:509.034 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:509.056 - 0.021ms returns 0
TBD84 000:509.078 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:509.099 - 0.021ms returns 0
TBD84 000:509.122 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:509.144 - 0.021ms returns 0
TBD84 000:509.166 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:509.187 - 0.021ms returns 0
TBD84 000:509.210 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:509.232 - 0.022ms returns 0x00000006
TBD84 000:509.254 JLINK_Go()
TBD84 000:509.279   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:512.233 - 2.978ms
TBD84 000:512.293 JLINK_IsHalted()
TBD84 000:514.583   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:515.293 - 2.999ms returns TRUE
TBD84 000:515.330 JLINK_ReadReg(R15 (PC))
TBD84 000:515.359 - 0.029ms returns 0x20200000
TBD84 000:515.388 JLINK_ClrBPEx(BPHandle = 0x00000006)
TBD84 000:515.413 - 0.025ms returns 0x00
TBD84 000:515.444 JLINK_ReadReg(R0)
TBD84 000:515.471 - 0.027ms returns 0x00000001
TBD84 000:515.500 JLINK_HasError()
TBD84 000:515.529 JLINK_WriteReg(R0, 0x00000800)
TBD84 000:515.570 - 0.040ms returns 0
TBD84 000:515.595 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:515.616 - 0.021ms returns 0
TBD84 000:515.639 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:515.658 - 0.019ms returns 0
TBD84 000:515.681 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:515.703 - 0.021ms returns 0
TBD84 000:515.725 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:515.745 - 0.019ms returns 0
TBD84 000:515.786 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:515.807 - 0.021ms returns 0
TBD84 000:515.830 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:515.850 - 0.020ms returns 0
TBD84 000:515.872 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:515.892 - 0.020ms returns 0
TBD84 000:515.915 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:515.935 - 0.020ms returns 0
TBD84 000:515.957 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:515.976 - 0.019ms returns 0
TBD84 000:516.006 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:516.026 - 0.020ms returns 0
TBD84 000:516.048 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:516.087 - 0.038ms returns 0
TBD84 000:516.114 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:516.138 - 0.023ms returns 0
TBD84 000:516.161 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:516.180 - 0.019ms returns 0
TBD84 000:516.203 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:516.222 - 0.019ms returns 0
TBD84 000:516.246 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:516.267 - 0.021ms returns 0
TBD84 000:516.290 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:516.310 - 0.020ms returns 0
TBD84 000:516.333 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:516.370 - 0.037ms returns 0
TBD84 000:516.395 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:516.415 - 0.019ms returns 0
TBD84 000:516.442 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:516.462 - 0.020ms returns 0
TBD84 000:516.484 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:516.505 - 0.020ms returns 0x00000007
TBD84 000:516.528 JLINK_Go()
TBD84 000:516.551   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:519.447 - 2.919ms
TBD84 000:519.485 JLINK_IsHalted()
TBD84 000:520.155 - 0.669ms returns FALSE
TBD84 000:520.188 JLINK_HasError()
TBD84 000:522.036 JLINK_IsHalted()
TBD84 000:524.310   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:525.055 - 3.018ms returns TRUE
TBD84 000:525.135 JLINK_ReadReg(R15 (PC))
TBD84 000:525.189 - 0.053ms returns 0x20200000
TBD84 000:525.237 JLINK_ClrBPEx(BPHandle = 0x00000007)
TBD84 000:525.285 - 0.048ms returns 0x00
TBD84 000:525.332 JLINK_ReadReg(R0)
TBD84 000:525.388 - 0.055ms returns 0x00000000
TBD84 000:525.960 JLINK_HasError()
TBD84 000:525.990 JLINK_WriteReg(R0, 0x00000C00)
TBD84 000:526.013 - 0.023ms returns 0
TBD84 000:526.053 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:526.077 - 0.023ms returns 0
TBD84 000:526.101 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:526.123 - 0.022ms returns 0
TBD84 000:526.146 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:526.168 - 0.022ms returns 0
TBD84 000:526.192 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:526.238 - 0.046ms returns 0
TBD84 000:526.263 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:526.287 - 0.023ms returns 0
TBD84 000:526.311 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:526.334 - 0.022ms returns 0
TBD84 000:526.357 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:526.381 - 0.023ms returns 0
TBD84 000:526.404 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:526.427 - 0.023ms returns 0
TBD84 000:526.453 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:526.476 - 0.022ms returns 0
TBD84 000:526.504 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:526.547 - 0.043ms returns 0
TBD84 000:526.575 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:526.599 - 0.023ms returns 0
TBD84 000:526.623 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:526.649 - 0.026ms returns 0
TBD84 000:526.672 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:526.712 - 0.039ms returns 0
TBD84 000:526.741 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:526.763 - 0.022ms returns 0
TBD84 000:526.787 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:526.809 - 0.022ms returns 0
TBD84 000:526.833 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:526.855 - 0.022ms returns 0
TBD84 000:526.880 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:526.902 - 0.022ms returns 0
TBD84 000:526.925 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:526.946 - 0.021ms returns 0
TBD84 000:526.997 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:527.024 - 0.027ms returns 0
TBD84 000:527.047 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:527.070 - 0.022ms returns 0x00000008
TBD84 000:527.093 JLINK_Go()
TBD84 000:527.118   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:529.948 - 2.854ms
TBD84 000:529.985 JLINK_IsHalted()
TBD84 000:532.322   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:533.047 - 3.062ms returns TRUE
TBD84 000:533.098 JLINK_ReadReg(R15 (PC))
TBD84 000:533.125 - 0.027ms returns 0x20200000
TBD84 000:533.149 JLINK_ClrBPEx(BPHandle = 0x00000008)
TBD84 000:533.172 - 0.023ms returns 0x00
TBD84 000:533.197 JLINK_ReadReg(R0)
TBD84 000:533.219 - 0.022ms returns 0x00000001
TBD84 000:533.255 JLINK_HasError()
TBD84 000:533.282 JLINK_WriteReg(R0, 0x00000C00)
TBD84 000:533.306 - 0.023ms returns 0
TBD84 000:533.329 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:533.352 - 0.022ms returns 0
TBD84 000:533.377 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:533.412 - 0.035ms returns 0
TBD84 000:533.441 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:533.467 - 0.026ms returns 0
TBD84 000:533.496 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:533.571 - 0.074ms returns 0
TBD84 000:533.599 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:533.622 - 0.022ms returns 0
TBD84 000:533.646 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:533.668 - 0.022ms returns 0
TBD84 000:533.692 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:533.739 - 0.047ms returns 0
TBD84 000:533.764 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:533.788 - 0.023ms returns 0
TBD84 000:533.811 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:533.833 - 0.022ms returns 0
TBD84 000:533.857 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:533.879 - 0.022ms returns 0
TBD84 000:533.903 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:533.953 - 0.049ms returns 0
TBD84 000:533.978 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:534.002 - 0.023ms returns 0
TBD84 000:534.027 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:534.050 - 0.022ms returns 0
TBD84 000:534.075 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:534.098 - 0.022ms returns 0
TBD84 000:534.145 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:534.173 - 0.028ms returns 0
TBD84 000:534.230 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:534.256 - 0.026ms returns 0
TBD84 000:534.281 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:534.304 - 0.023ms returns 0
TBD84 000:534.328 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:534.351 - 0.022ms returns 0
TBD84 000:534.374 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:534.397 - 0.022ms returns 0
TBD84 000:534.424 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:534.447 - 0.024ms returns 0x00000009
TBD84 000:534.472 JLINK_Go()
TBD84 000:534.502   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:537.598 - 3.126ms
TBD84 000:537.661 JLINK_IsHalted()
TBD84 000:538.417 - 0.755ms returns FALSE
TBD84 000:538.445 JLINK_HasError()
TBD84 000:540.212 JLINK_IsHalted()
TBD84 000:542.476   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:543.188 - 2.975ms returns TRUE
TBD84 000:543.248 JLINK_ReadReg(R15 (PC))
TBD84 000:543.293 - 0.044ms returns 0x20200000
TBD84 000:543.337 JLINK_ClrBPEx(BPHandle = 0x00000009)
TBD84 000:543.380 - 0.043ms returns 0x00
TBD84 000:543.435 JLINK_ReadReg(R0)
TBD84 000:543.477 - 0.042ms returns 0x00000000
TBD84 000:543.961 JLINK_HasError()
TBD84 000:543.990 JLINK_WriteReg(R0, 0x00001000)
TBD84 000:544.012 - 0.022ms returns 0
TBD84 000:544.035 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:544.057 - 0.021ms returns 0
TBD84 000:544.079 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:544.101 - 0.021ms returns 0
TBD84 000:544.145 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:544.169 - 0.023ms returns 0
TBD84 000:544.192 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:544.215 - 0.022ms returns 0
TBD84 000:544.237 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:544.260 - 0.022ms returns 0
TBD84 000:544.285 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:544.308 - 0.022ms returns 0
TBD84 000:544.330 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:544.354 - 0.023ms returns 0
TBD84 000:544.377 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:544.398 - 0.021ms returns 0
TBD84 000:544.422 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:544.443 - 0.021ms returns 0
TBD84 000:544.467 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:544.489 - 0.021ms returns 0
TBD84 000:544.511 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:544.537 - 0.025ms returns 0
TBD84 000:544.560 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:544.644 - 0.084ms returns 0
TBD84 000:544.675 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:544.702 - 0.027ms returns 0
TBD84 000:544.726 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:544.748 - 0.022ms returns 0
TBD84 000:544.771 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:544.826 - 0.054ms returns 0
TBD84 000:544.853 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:544.886 - 0.033ms returns 0
TBD84 000:544.913 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:544.934 - 0.021ms returns 0
TBD84 000:544.958 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:544.979 - 0.021ms returns 0
TBD84 000:545.023 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:545.047 - 0.023ms returns 0
TBD84 000:545.070 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:545.120 - 0.049ms returns 0x0000000A
TBD84 000:545.144 JLINK_Go()
TBD84 000:545.169   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:548.065 - 2.921ms
TBD84 000:548.098 JLINK_IsHalted()
TBD84 000:550.310   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:551.041 - 2.942ms returns TRUE
TBD84 000:551.076 JLINK_ReadReg(R15 (PC))
TBD84 000:551.098 - 0.022ms returns 0x20200000
TBD84 000:551.123 JLINK_ClrBPEx(BPHandle = 0x0000000A)
TBD84 000:551.145 - 0.021ms returns 0x00
TBD84 000:551.170 JLINK_ReadReg(R0)
TBD84 000:551.207 - 0.036ms returns 0x00000001
TBD84 000:551.234 JLINK_HasError()
TBD84 000:551.261 JLINK_WriteReg(R0, 0x00001000)
TBD84 000:551.284 - 0.023ms returns 0
TBD84 000:551.308 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:551.345 - 0.036ms returns 0
TBD84 000:551.374 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:551.400 - 0.026ms returns 0
TBD84 000:551.425 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:551.446 - 0.021ms returns 0
TBD84 000:551.470 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:551.491 - 0.021ms returns 0
TBD84 000:551.529 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:551.555 - 0.025ms returns 0
TBD84 000:551.580 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:551.602 - 0.022ms returns 0
TBD84 000:551.626 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:551.647 - 0.021ms returns 0
TBD84 000:551.675 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:551.710 - 0.035ms returns 0
TBD84 000:551.737 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:551.758 - 0.021ms returns 0
TBD84 000:551.782 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:551.803 - 0.021ms returns 0
TBD84 000:551.827 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:551.849 - 0.022ms returns 0
TBD84 000:551.945 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:552.001 - 0.056ms returns 0
TBD84 000:552.051 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:552.096 - 0.045ms returns 0
TBD84 000:552.144 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:552.186 - 0.043ms returns 0
TBD84 000:552.234 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:552.280 - 0.046ms returns 0
TBD84 000:552.330 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:552.373 - 0.043ms returns 0
TBD84 000:552.424 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:552.488 - 0.063ms returns 0
TBD84 000:552.515 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:552.543 - 0.028ms returns 0
TBD84 000:552.569 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:552.592 - 0.022ms returns 0
TBD84 000:552.616 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:552.638 - 0.022ms returns 0x0000000B
TBD84 000:552.662 JLINK_Go()
TBD84 000:552.686   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:555.585 - 2.922ms
TBD84 000:555.617 JLINK_IsHalted()
TBD84 000:556.275 - 0.657ms returns FALSE
TBD84 000:556.317 JLINK_HasError()
TBD84 000:557.407 JLINK_IsHalted()
TBD84 000:559.692   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:560.415 - 3.007ms returns TRUE
TBD84 000:560.447 JLINK_ReadReg(R15 (PC))
TBD84 000:560.471 - 0.024ms returns 0x20200000
TBD84 000:560.495 JLINK_ClrBPEx(BPHandle = 0x0000000B)
TBD84 000:560.516 - 0.021ms returns 0x00
TBD84 000:560.543 JLINK_ReadReg(R0)
TBD84 000:560.565 - 0.022ms returns 0x00000000
TBD84 000:561.499 JLINK_HasError()
TBD84 000:561.530 JLINK_WriteReg(R0, 0x00001400)
TBD84 000:561.552 - 0.022ms returns 0
TBD84 000:561.579 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:561.601 - 0.021ms returns 0
TBD84 000:561.623 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:561.645 - 0.021ms returns 0
TBD84 000:561.679 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:561.701 - 0.022ms returns 0
TBD84 000:561.724 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:561.745 - 0.021ms returns 0
TBD84 000:561.767 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:561.789 - 0.021ms returns 0
TBD84 000:561.811 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:561.834 - 0.023ms returns 0
TBD84 000:561.857 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:561.878 - 0.021ms returns 0
TBD84 000:561.901 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:561.922 - 0.021ms returns 0
TBD84 000:561.944 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:561.965 - 0.020ms returns 0
TBD84 000:561.987 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:562.040 - 0.052ms returns 0
TBD84 000:562.062 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:562.083 - 0.020ms returns 0
TBD84 000:562.106 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:562.155 - 0.049ms returns 0
TBD84 000:562.180 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:562.201 - 0.022ms returns 0
TBD84 000:562.225 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:562.246 - 0.021ms returns 0
TBD84 000:562.270 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:562.292 - 0.022ms returns 0
TBD84 000:562.329 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:562.353 - 0.023ms returns 0
TBD84 000:562.376 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:562.398 - 0.021ms returns 0
TBD84 000:562.421 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:562.442 - 0.021ms returns 0
TBD84 000:562.468 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:562.490 - 0.022ms returns 0
TBD84 000:562.513 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:562.535 - 0.022ms returns 0x0000000C
TBD84 000:562.560 JLINK_Go()
TBD84 000:562.587   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:565.585 - 3.024ms
TBD84 000:565.615 JLINK_IsHalted()
TBD84 000:567.813   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:568.540 - 2.924ms returns TRUE
TBD84 000:568.593 JLINK_ReadReg(R15 (PC))
TBD84 000:568.615 - 0.022ms returns 0x20200000
TBD84 000:568.636 JLINK_ClrBPEx(BPHandle = 0x0000000C)
TBD84 000:568.656 - 0.020ms returns 0x00
TBD84 000:568.677 JLINK_ReadReg(R0)
TBD84 000:568.697 - 0.019ms returns 0x00000001
TBD84 000:568.718 JLINK_HasError()
TBD84 000:568.739 JLINK_WriteReg(R0, 0x00001400)
TBD84 000:568.759 - 0.020ms returns 0
TBD84 000:568.781 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:568.801 - 0.020ms returns 0
TBD84 000:568.822 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:568.842 - 0.019ms returns 0
TBD84 000:568.863 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:568.897 - 0.034ms returns 0
TBD84 000:568.920 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:568.940 - 0.020ms returns 0
TBD84 000:568.961 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:568.981 - 0.020ms returns 0
TBD84 000:569.003 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:569.023 - 0.019ms returns 0
TBD84 000:569.044 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:569.064 - 0.019ms returns 0
TBD84 000:569.089 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:569.109 - 0.020ms returns 0
TBD84 000:569.130 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:569.149 - 0.019ms returns 0
TBD84 000:569.170 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:569.190 - 0.019ms returns 0
TBD84 000:569.211 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:569.261 - 0.050ms returns 0
TBD84 000:569.284 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:569.304 - 0.020ms returns 0
TBD84 000:569.325 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:569.347 - 0.022ms returns 0
TBD84 000:569.378 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:569.400 - 0.021ms returns 0
TBD84 000:569.421 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:569.442 - 0.020ms returns 0
TBD84 000:569.463 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:569.483 - 0.020ms returns 0
TBD84 000:569.505 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:569.525 - 0.019ms returns 0
TBD84 000:569.546 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:569.613 - 0.066ms returns 0
TBD84 000:569.635 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:569.655 - 0.020ms returns 0
TBD84 000:569.676 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:569.696 - 0.020ms returns 0x0000000D
TBD84 000:569.737 JLINK_Go()
TBD84 000:569.767   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:572.582 - 2.844ms
TBD84 000:572.611 JLINK_IsHalted()
TBD84 000:573.270 - 0.659ms returns FALSE
TBD84 000:573.300 JLINK_HasError()
TBD84 000:575.088 JLINK_IsHalted()
TBD84 000:577.321   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:578.070 - 2.980ms returns TRUE
TBD84 000:578.127 JLINK_ReadReg(R15 (PC))
TBD84 000:578.170 - 0.043ms returns 0x20200000
TBD84 000:578.214 JLINK_ClrBPEx(BPHandle = 0x0000000D)
TBD84 000:578.257 - 0.043ms returns 0x00
TBD84 000:578.302 JLINK_ReadReg(R0)
TBD84 000:578.343 - 0.041ms returns 0x00000000
TBD84 000:578.927 JLINK_HasError()
TBD84 000:578.953 JLINK_WriteReg(R0, 0x00001800)
TBD84 000:578.995 - 0.041ms returns 0
TBD84 000:579.023 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:579.045 - 0.021ms returns 0
TBD84 000:579.066 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:579.087 - 0.020ms returns 0
TBD84 000:579.109 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:579.129 - 0.020ms returns 0
TBD84 000:579.168 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:579.193 - 0.025ms returns 0
TBD84 000:579.215 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:579.239 - 0.023ms returns 0
TBD84 000:579.261 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:579.281 - 0.020ms returns 0
TBD84 000:579.304 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:579.344 - 0.039ms returns 0
TBD84 000:579.371 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:579.392 - 0.021ms returns 0
TBD84 000:579.413 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:579.434 - 0.020ms returns 0
TBD84 000:579.456 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:579.494 - 0.037ms returns 0
TBD84 000:579.520 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:579.543 - 0.023ms returns 0
TBD84 000:579.566 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:579.587 - 0.020ms returns 0
TBD84 000:579.612 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:579.653 - 0.040ms returns 0
TBD84 000:579.675 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:579.698 - 0.023ms returns 0
TBD84 000:579.721 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:579.741 - 0.020ms returns 0
TBD84 000:579.763 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:579.803 - 0.039ms returns 0
TBD84 000:579.825 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:579.846 - 0.020ms returns 0
TBD84 000:579.867 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:579.906 - 0.038ms returns 0
TBD84 000:579.934 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:579.967 - 0.033ms returns 0
TBD84 000:579.990 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:580.034 - 0.043ms returns 0x0000000E
TBD84 000:580.056 JLINK_Go()
TBD84 000:580.080   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:582.949 - 2.892ms
TBD84 000:582.982 JLINK_IsHalted()
TBD84 000:585.194   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:585.924 - 2.942ms returns TRUE
TBD84 000:585.958 JLINK_ReadReg(R15 (PC))
TBD84 000:585.983 - 0.024ms returns 0x20200000
TBD84 000:586.008 JLINK_ClrBPEx(BPHandle = 0x0000000E)
TBD84 000:586.030 - 0.022ms returns 0x00
TBD84 000:586.054 JLINK_ReadReg(R0)
TBD84 000:586.076 - 0.022ms returns 0x00000001
TBD84 000:586.101 JLINK_HasError()
TBD84 000:586.126 JLINK_WriteReg(R0, 0x00001800)
TBD84 000:586.148 - 0.021ms returns 0
TBD84 000:586.172 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:586.193 - 0.021ms returns 0
TBD84 000:586.217 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:586.239 - 0.021ms returns 0
TBD84 000:586.263 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:586.284 - 0.021ms returns 0
TBD84 000:586.310 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:586.331 - 0.021ms returns 0
TBD84 000:586.356 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:586.378 - 0.022ms returns 0
TBD84 000:586.402 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:586.424 - 0.022ms returns 0
TBD84 000:586.448 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:586.470 - 0.021ms returns 0
TBD84 000:586.498 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:586.520 - 0.026ms returns 0
TBD84 000:586.544 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:586.567 - 0.022ms returns 0
TBD84 000:586.595 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:586.617 - 0.021ms returns 0
TBD84 000:586.641 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:586.663 - 0.021ms returns 0
TBD84 000:586.688 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:586.710 - 0.021ms returns 0
TBD84 000:586.736 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:586.758 - 0.022ms returns 0
TBD84 000:586.783 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:586.805 - 0.022ms returns 0
TBD84 000:586.848 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:586.873 - 0.024ms returns 0
TBD84 000:586.898 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:586.921 - 0.022ms returns 0
TBD84 000:586.946 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:586.971 - 0.024ms returns 0
TBD84 000:586.996 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:587.074 - 0.078ms returns 0
TBD84 000:587.107 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:587.130 - 0.023ms returns 0
TBD84 000:587.155 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:587.178 - 0.023ms returns 0x0000000F
TBD84 000:587.202 JLINK_Go()
TBD84 000:587.228   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:590.355 - 3.151ms
TBD84 000:590.420 JLINK_IsHalted()
TBD84 000:591.159 - 0.738ms returns FALSE
TBD84 000:591.192 JLINK_HasError()
TBD84 000:592.642 JLINK_IsHalted()
TBD84 000:594.809   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:595.522 - 2.879ms returns TRUE
TBD84 000:595.555 JLINK_ReadReg(R15 (PC))
TBD84 000:595.577 - 0.022ms returns 0x20200000
TBD84 000:595.600 JLINK_ClrBPEx(BPHandle = 0x0000000F)
TBD84 000:595.622 - 0.021ms returns 0x00
TBD84 000:595.649 JLINK_ReadReg(R0)
TBD84 000:595.671 - 0.022ms returns 0x00000000
TBD84 000:596.146 JLINK_HasError()
TBD84 000:596.176 JLINK_WriteReg(R0, 0x00001C00)
TBD84 000:596.198 - 0.022ms returns 0
TBD84 000:596.221 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:596.243 - 0.022ms returns 0
TBD84 000:596.266 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:596.287 - 0.021ms returns 0
TBD84 000:596.332 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:596.360 - 0.028ms returns 0
TBD84 000:596.383 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:596.404 - 0.021ms returns 0
TBD84 000:596.429 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:596.451 - 0.022ms returns 0
TBD84 000:596.486 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:596.512 - 0.026ms returns 0
TBD84 000:596.535 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:596.557 - 0.022ms returns 0
TBD84 000:596.580 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:596.602 - 0.022ms returns 0
TBD84 000:596.637 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:596.665 - 0.027ms returns 0
TBD84 000:596.689 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:596.711 - 0.021ms returns 0
TBD84 000:596.733 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:596.755 - 0.021ms returns 0
TBD84 000:596.799 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:596.827 - 0.027ms returns 0
TBD84 000:596.861 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:596.883 - 0.022ms returns 0
TBD84 000:596.905 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:596.928 - 0.022ms returns 0
TBD84 000:596.952 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:596.993 - 0.041ms returns 0
TBD84 000:597.022 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:597.044 - 0.022ms returns 0
TBD84 000:597.067 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:597.089 - 0.021ms returns 0
TBD84 000:597.111 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:597.133 - 0.021ms returns 0
TBD84 000:597.160 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:597.181 - 0.021ms returns 0
TBD84 000:597.203 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:597.225 - 0.022ms returns 0x00000010
TBD84 000:597.248 JLINK_Go()
TBD84 000:597.274   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:600.210 - 2.962ms
TBD84 000:600.241 JLINK_IsHalted()
TBD84 000:602.448   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:603.181 - 2.940ms returns TRUE
TBD84 000:603.211 JLINK_ReadReg(R15 (PC))
TBD84 000:603.234 - 0.022ms returns 0x20200000
TBD84 000:603.257 JLINK_ClrBPEx(BPHandle = 0x00000010)
TBD84 000:603.280 - 0.022ms returns 0x00
TBD84 000:603.303 JLINK_ReadReg(R0)
TBD84 000:603.325 - 0.021ms returns 0x00000001
TBD84 000:603.347 JLINK_HasError()
TBD84 000:603.370 JLINK_WriteReg(R0, 0x00001C00)
TBD84 000:603.394 - 0.023ms returns 0
TBD84 000:603.422 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:603.447 - 0.024ms returns 0
TBD84 000:603.501 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:603.524 - 0.023ms returns 0
TBD84 000:603.547 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:603.569 - 0.022ms returns 0
TBD84 000:603.592 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:603.616 - 0.023ms returns 0
TBD84 000:603.639 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:603.686 - 0.046ms returns 0
TBD84 000:603.732 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:603.777 - 0.044ms returns 0
TBD84 000:603.823 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:603.867 - 0.044ms returns 0
TBD84 000:603.912 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:603.956 - 0.043ms returns 0
TBD84 000:604.001 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:604.044 - 0.043ms returns 0
TBD84 000:604.088 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:604.133 - 0.044ms returns 0
TBD84 000:604.180 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:604.252 - 0.070ms returns 0
TBD84 000:604.300 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:604.344 - 0.043ms returns 0
TBD84 000:604.439 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:604.492 - 0.053ms returns 0
TBD84 000:604.537 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:604.580 - 0.043ms returns 0
TBD84 000:604.629 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:604.651 - 0.022ms returns 0
TBD84 000:604.675 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:604.703 - 0.027ms returns 0
TBD84 000:604.726 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:604.748 - 0.021ms returns 0
TBD84 000:604.771 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:604.792 - 0.021ms returns 0
TBD84 000:604.816 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:604.849 - 0.032ms returns 0
TBD84 000:604.877 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:604.900 - 0.023ms returns 0x00000011
TBD84 000:604.922 JLINK_Go()
TBD84 000:604.956   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:607.898 - 2.975ms
TBD84 000:607.947 JLINK_IsHalted()
TBD84 000:608.662 - 0.714ms returns FALSE
TBD84 000:608.692 JLINK_HasError()
TBD84 000:610.752 JLINK_IsHalted()
TBD84 000:613.069   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:613.797 - 3.045ms returns TRUE
TBD84 000:613.828 JLINK_ReadReg(R15 (PC))
TBD84 000:613.851 - 0.023ms returns 0x20200000
TBD84 000:613.874 JLINK_ClrBPEx(BPHandle = 0x00000011)
TBD84 000:613.910 - 0.034ms returns 0x00
TBD84 000:613.941 JLINK_ReadReg(R0)
TBD84 000:613.964 - 0.022ms returns 0x00000000
TBD84 000:614.514 JLINK_HasError()
TBD84 000:614.546 JLINK_WriteReg(R0, 0x00002000)
TBD84 000:614.569 - 0.023ms returns 0
TBD84 000:614.592 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:614.613 - 0.021ms returns 0
TBD84 000:614.637 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:614.659 - 0.021ms returns 0
TBD84 000:614.685 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:614.731 - 0.045ms returns 0
TBD84 000:614.761 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:614.783 - 0.022ms returns 0
TBD84 000:614.806 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:614.828 - 0.022ms returns 0
TBD84 000:614.863 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:614.896 - 0.032ms returns 0
TBD84 000:614.924 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:614.946 - 0.022ms returns 0
TBD84 000:614.969 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:614.991 - 0.021ms returns 0
TBD84 000:615.014 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:615.036 - 0.021ms returns 0
TBD84 000:615.080 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:615.103 - 0.023ms returns 0
TBD84 000:615.126 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:615.148 - 0.021ms returns 0
TBD84 000:615.171 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:615.192 - 0.021ms returns 0
TBD84 000:615.216 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:615.238 - 0.021ms returns 0
TBD84 000:615.261 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:615.289 - 0.028ms returns 0
TBD84 000:615.313 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:615.335 - 0.022ms returns 0
TBD84 000:615.370 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:615.397 - 0.026ms returns 0
TBD84 000:615.420 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:615.442 - 0.022ms returns 0
TBD84 000:615.465 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:615.487 - 0.021ms returns 0
TBD84 000:615.510 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:615.532 - 0.022ms returns 0
TBD84 000:615.585 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:615.609 - 0.024ms returns 0x00000012
TBD84 000:615.632 JLINK_Go()
TBD84 000:615.658   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:618.578 - 2.945ms
TBD84 000:618.612 JLINK_IsHalted()
TBD84 000:620.963   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:621.684 - 3.072ms returns TRUE
TBD84 000:621.718 JLINK_ReadReg(R15 (PC))
TBD84 000:621.742 - 0.023ms returns 0x20200000
TBD84 000:621.766 JLINK_ClrBPEx(BPHandle = 0x00000012)
TBD84 000:621.788 - 0.021ms returns 0x00
TBD84 000:621.812 JLINK_ReadReg(R0)
TBD84 000:621.834 - 0.022ms returns 0x00000001
TBD84 000:621.858 JLINK_HasError()
TBD84 000:621.883 JLINK_WriteReg(R0, 0x00002000)
TBD84 000:621.905 - 0.022ms returns 0
TBD84 000:621.960 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:621.986 - 0.026ms returns 0
TBD84 000:622.011 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:622.032 - 0.021ms returns 0
TBD84 000:622.064 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:622.086 - 0.022ms returns 0
TBD84 000:622.124 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:622.150 - 0.026ms returns 0
TBD84 000:622.175 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:622.197 - 0.021ms returns 0
TBD84 000:622.222 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:622.244 - 0.021ms returns 0
TBD84 000:622.282 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:622.308 - 0.025ms returns 0
TBD84 000:622.332 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:622.354 - 0.022ms returns 0
TBD84 000:622.379 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:622.402 - 0.022ms returns 0
TBD84 000:622.427 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:622.449 - 0.021ms returns 0
TBD84 000:622.474 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:622.497 - 0.023ms returns 0
TBD84 000:622.522 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:622.548 - 0.026ms returns 0
TBD84 000:622.573 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:622.594 - 0.021ms returns 0
TBD84 000:622.645 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:622.668 - 0.023ms returns 0
TBD84 000:622.693 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:622.716 - 0.022ms returns 0
TBD84 000:622.740 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:622.763 - 0.022ms returns 0
TBD84 000:622.787 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:622.823 - 0.035ms returns 0
TBD84 000:622.850 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:622.898 - 0.047ms returns 0
TBD84 000:622.931 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:622.954 - 0.023ms returns 0
TBD84 000:622.980 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:623.003 - 0.023ms returns 0x00000013
TBD84 000:623.028 JLINK_Go()
TBD84 000:623.054   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:626.075 - 3.046ms
TBD84 000:626.109 JLINK_IsHalted()
TBD84 000:626.780 - 0.671ms returns FALSE
TBD84 000:626.814 JLINK_HasError()
TBD84 000:628.147 JLINK_IsHalted()
TBD84 000:630.460   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:631.160 - 3.013ms returns TRUE
TBD84 000:631.187 JLINK_ReadReg(R15 (PC))
TBD84 000:631.208 - 0.020ms returns 0x20200000
TBD84 000:631.228 JLINK_ClrBPEx(BPHandle = 0x00000013)
TBD84 000:631.247 - 0.019ms returns 0x00
TBD84 000:631.267 JLINK_ReadReg(R0)
TBD84 000:631.286 - 0.018ms returns 0x00000000
TBD84 000:631.717 JLINK_HasError()
TBD84 000:631.742 JLINK_WriteReg(R0, 0x00002400)
TBD84 000:631.762 - 0.019ms returns 0
TBD84 000:631.782 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:631.801 - 0.019ms returns 0
TBD84 000:631.821 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:631.840 - 0.019ms returns 0
TBD84 000:631.861 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:631.880 - 0.018ms returns 0
TBD84 000:631.900 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:631.919 - 0.018ms returns 0
TBD84 000:631.939 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:631.957 - 0.018ms returns 0
TBD84 000:631.978 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:631.997 - 0.019ms returns 0
TBD84 000:632.017 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:632.036 - 0.019ms returns 0
TBD84 000:632.055 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:632.074 - 0.018ms returns 0
TBD84 000:632.094 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:632.113 - 0.018ms returns 0
TBD84 000:632.133 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:632.152 - 0.018ms returns 0
TBD84 000:632.172 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:632.190 - 0.018ms returns 0
TBD84 000:632.210 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:632.231 - 0.020ms returns 0
TBD84 000:632.252 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:632.271 - 0.019ms returns 0
TBD84 000:632.291 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:632.309 - 0.018ms returns 0
TBD84 000:632.329 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:632.348 - 0.018ms returns 0
TBD84 000:632.368 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:632.387 - 0.019ms returns 0
TBD84 000:632.407 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:632.426 - 0.018ms returns 0
TBD84 000:632.446 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:632.465 - 0.019ms returns 0
TBD84 000:632.485 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:632.504 - 0.019ms returns 0
TBD84 000:632.524 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:632.543 - 0.019ms returns 0x00000014
TBD84 000:632.563 JLINK_Go()
TBD84 000:632.585   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:635.449 - 2.885ms
TBD84 000:635.477 JLINK_IsHalted()
TBD84 000:637.704   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:638.417 - 2.940ms returns TRUE
TBD84 000:638.444 JLINK_ReadReg(R15 (PC))
TBD84 000:638.464 - 0.020ms returns 0x20200000
TBD84 000:638.484 JLINK_ClrBPEx(BPHandle = 0x00000014)
TBD84 000:638.503 - 0.019ms returns 0x00
TBD84 000:638.522 JLINK_ReadReg(R0)
TBD84 000:638.541 - 0.018ms returns 0x00000001
TBD84 000:638.561 JLINK_HasError()
TBD84 000:638.581 JLINK_WriteReg(R0, 0x00002400)
TBD84 000:638.600 - 0.019ms returns 0
TBD84 000:638.620 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:638.639 - 0.018ms returns 0
TBD84 000:638.659 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:638.677 - 0.018ms returns 0
TBD84 000:638.701 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:638.720 - 0.018ms returns 0
TBD84 000:638.739 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:638.758 - 0.018ms returns 0
TBD84 000:638.778 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:638.796 - 0.018ms returns 0
TBD84 000:638.816 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:638.835 - 0.018ms returns 0
TBD84 000:638.854 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:638.873 - 0.018ms returns 0
TBD84 000:638.892 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:638.911 - 0.018ms returns 0
TBD84 000:638.931 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:638.949 - 0.018ms returns 0
TBD84 000:638.969 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:638.987 - 0.018ms returns 0
TBD84 000:639.007 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:639.026 - 0.018ms returns 0
TBD84 000:639.045 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:639.064 - 0.018ms returns 0
TBD84 000:639.084 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:639.102 - 0.018ms returns 0
TBD84 000:639.122 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:639.140 - 0.018ms returns 0
TBD84 000:639.160 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:639.179 - 0.018ms returns 0
TBD84 000:639.199 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:639.217 - 0.018ms returns 0
TBD84 000:639.237 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:639.255 - 0.018ms returns 0
TBD84 000:639.275 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:639.294 - 0.018ms returns 0
TBD84 000:639.313 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:639.332 - 0.018ms returns 0
TBD84 000:639.352 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:639.371 - 0.019ms returns 0x00000015
TBD84 000:639.390 JLINK_Go()
TBD84 000:639.411   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:642.458 - 3.067ms
TBD84 000:642.484 JLINK_IsHalted()
TBD84 000:643.176 - 0.691ms returns FALSE
TBD84 000:643.202 JLINK_HasError()
TBD84 000:647.200 JLINK_IsHalted()
TBD84 000:649.429   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:650.153 - 2.953ms returns TRUE
TBD84 000:650.180 JLINK_ReadReg(R15 (PC))
TBD84 000:650.201 - 0.020ms returns 0x20200000
TBD84 000:650.222 JLINK_ClrBPEx(BPHandle = 0x00000015)
TBD84 000:650.241 - 0.019ms returns 0x00
TBD84 000:650.261 JLINK_ReadReg(R0)
TBD84 000:650.281 - 0.019ms returns 0x00000000
TBD84 000:651.148 JLINK_HasError()
TBD84 000:651.209 JLINK_WriteReg(R0, 0x00002800)
TBD84 000:651.256 - 0.046ms returns 0
TBD84 000:651.302 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:651.349 - 0.046ms returns 0
TBD84 000:651.397 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:651.441 - 0.043ms returns 0
TBD84 000:651.494 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:651.538 - 0.044ms returns 0
TBD84 000:651.587 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:651.631 - 0.043ms returns 0
TBD84 000:651.680 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:651.711 - 0.030ms returns 0
TBD84 000:651.733 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:651.752 - 0.019ms returns 0
TBD84 000:651.774 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:651.794 - 0.019ms returns 0
TBD84 000:651.816 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:651.835 - 0.019ms returns 0
TBD84 000:651.857 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:651.877 - 0.019ms returns 0
TBD84 000:651.899 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:651.918 - 0.019ms returns 0
TBD84 000:651.940 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:651.959 - 0.019ms returns 0
TBD84 000:651.981 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:652.001 - 0.019ms returns 0
TBD84 000:652.023 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:652.042 - 0.019ms returns 0
TBD84 000:652.064 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:652.084 - 0.019ms returns 0
TBD84 000:652.106 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:652.125 - 0.019ms returns 0
TBD84 000:652.147 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:652.167 - 0.019ms returns 0
TBD84 000:652.189 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:652.208 - 0.019ms returns 0
TBD84 000:652.230 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:652.250 - 0.019ms returns 0
TBD84 000:652.272 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:652.291 - 0.019ms returns 0
TBD84 000:652.313 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:652.333 - 0.020ms returns 0x00000016
TBD84 000:652.355 JLINK_Go()
TBD84 000:652.378   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:655.462 - 3.106ms
TBD84 000:655.491 JLINK_IsHalted()
TBD84 000:657.703   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:658.409 - 2.917ms returns TRUE
TBD84 000:658.439 JLINK_ReadReg(R15 (PC))
TBD84 000:658.460 - 0.020ms returns 0x20200000
TBD84 000:658.482 JLINK_ClrBPEx(BPHandle = 0x00000016)
TBD84 000:658.502 - 0.019ms returns 0x00
TBD84 000:658.524 JLINK_ReadReg(R0)
TBD84 000:658.543 - 0.019ms returns 0x00000001
TBD84 000:658.566 JLINK_HasError()
TBD84 000:658.588 JLINK_WriteReg(R0, 0x00002800)
TBD84 000:658.607 - 0.019ms returns 0
TBD84 000:658.629 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:658.649 - 0.019ms returns 0
TBD84 000:658.671 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:658.690 - 0.019ms returns 0
TBD84 000:658.716 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:658.735 - 0.019ms returns 0
TBD84 000:658.757 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:658.777 - 0.019ms returns 0
TBD84 000:658.799 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:658.818 - 0.019ms returns 0
TBD84 000:658.840 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:658.859 - 0.019ms returns 0
TBD84 000:658.881 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:658.901 - 0.019ms returns 0
TBD84 000:658.923 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:658.942 - 0.019ms returns 0
TBD84 000:658.964 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:658.984 - 0.019ms returns 0
TBD84 000:659.006 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:659.025 - 0.019ms returns 0
TBD84 000:659.047 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:659.067 - 0.019ms returns 0
TBD84 000:659.089 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:659.108 - 0.019ms returns 0
TBD84 000:659.130 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:659.150 - 0.019ms returns 0
TBD84 000:659.172 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:659.191 - 0.019ms returns 0
TBD84 000:659.213 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:659.233 - 0.019ms returns 0
TBD84 000:659.255 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:659.274 - 0.019ms returns 0
TBD84 000:659.296 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:659.315 - 0.019ms returns 0
TBD84 000:659.337 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:659.362 - 0.024ms returns 0
TBD84 000:659.384 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:659.405 - 0.021ms returns 0
TBD84 000:659.428 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:659.448 - 0.020ms returns 0x00000017
TBD84 000:659.471 JLINK_Go()
TBD84 000:659.493   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:662.453 - 2.981ms
TBD84 000:662.483 JLINK_IsHalted()
TBD84 000:663.181 - 0.697ms returns FALSE
TBD84 000:663.246 JLINK_HasError()
TBD84 000:665.048 JLINK_IsHalted()
TBD84 000:667.318   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:668.042 - 2.993ms returns TRUE
TBD84 000:668.069 JLINK_ReadReg(R15 (PC))
TBD84 000:668.090 - 0.020ms returns 0x20200000
TBD84 000:668.111 JLINK_ClrBPEx(BPHandle = 0x00000017)
TBD84 000:668.130 - 0.019ms returns 0x00
TBD84 000:668.151 JLINK_ReadReg(R0)
TBD84 000:668.170 - 0.019ms returns 0x00000000
TBD84 000:668.661 JLINK_HasError()
TBD84 000:668.689 JLINK_WriteReg(R0, 0x00002C00)
TBD84 000:668.709 - 0.020ms returns 0
TBD84 000:668.730 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:668.753 - 0.023ms returns 0
TBD84 000:668.774 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:668.793 - 0.019ms returns 0
TBD84 000:668.814 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:668.833 - 0.019ms returns 0
TBD84 000:668.853 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:668.872 - 0.019ms returns 0
TBD84 000:668.893 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:668.912 - 0.019ms returns 0
TBD84 000:668.932 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:668.951 - 0.019ms returns 0
TBD84 000:668.972 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:668.991 - 0.019ms returns 0
TBD84 000:669.011 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:669.036 - 0.024ms returns 0
TBD84 000:669.057 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:669.076 - 0.019ms returns 0
TBD84 000:669.096 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:669.116 - 0.019ms returns 0
TBD84 000:669.136 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:669.155 - 0.019ms returns 0
TBD84 000:669.176 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:669.195 - 0.019ms returns 0
TBD84 000:669.215 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:669.235 - 0.019ms returns 0
TBD84 000:669.255 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:669.274 - 0.019ms returns 0
TBD84 000:669.295 JLINK_WriteReg(R15 (PC), 0x20200020)
TBD84 000:669.314 - 0.019ms returns 0
TBD84 000:669.334 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:669.353 - 0.019ms returns 0
TBD84 000:669.374 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:669.393 - 0.019ms returns 0
TBD84 000:669.413 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:669.432 - 0.019ms returns 0
TBD84 000:669.453 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:669.473 - 0.020ms returns 0
TBD84 000:669.497 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:669.517 - 0.019ms returns 0x00000018
TBD84 000:669.537 JLINK_Go()
TBD84 000:669.559   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:672.582 - 3.045ms
TBD84 000:672.610 JLINK_IsHalted()
TBD84 000:674.814   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:675.575 - 2.963ms returns TRUE
TBD84 000:675.643 JLINK_ReadReg(R15 (PC))
TBD84 000:675.689 - 0.045ms returns 0x20200000
TBD84 000:675.735 JLINK_ClrBPEx(BPHandle = 0x00000018)
TBD84 000:675.762 - 0.028ms returns 0x00
TBD84 000:675.782 JLINK_ReadReg(R0)
TBD84 000:675.802 - 0.019ms returns 0x00000001
TBD84 000:675.822 JLINK_HasError()
TBD84 000:675.842 JLINK_WriteReg(R0, 0x00002C00)
TBD84 000:675.866 - 0.023ms returns 0
TBD84 000:675.886 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:675.905 - 0.019ms returns 0
TBD84 000:675.925 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:675.945 - 0.019ms returns 0
TBD84 000:675.969 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:675.988 - 0.019ms returns 0
TBD84 000:676.008 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:676.028 - 0.019ms returns 0
TBD84 000:676.048 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:676.067 - 0.019ms returns 0
TBD84 000:676.087 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:676.106 - 0.019ms returns 0
TBD84 000:676.126 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:676.145 - 0.019ms returns 0
TBD84 000:676.166 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:676.187 - 0.020ms returns 0
TBD84 000:676.208 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:676.227 - 0.019ms returns 0
TBD84 000:676.247 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:676.266 - 0.019ms returns 0
TBD84 000:676.286 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:676.305 - 0.019ms returns 0
TBD84 000:676.325 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:676.345 - 0.019ms returns 0
TBD84 000:676.365 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:676.384 - 0.019ms returns 0
TBD84 000:676.404 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:676.423 - 0.019ms returns 0
TBD84 000:676.444 JLINK_WriteReg(R15 (PC), 0x20200098)
TBD84 000:676.463 - 0.019ms returns 0
TBD84 000:676.483 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:676.502 - 0.019ms returns 0
TBD84 000:676.522 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:676.541 - 0.019ms returns 0
TBD84 000:676.562 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:676.581 - 0.019ms returns 0
TBD84 000:676.601 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:676.620 - 0.019ms returns 0
TBD84 000:676.640 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:676.664 - 0.023ms returns 0x00000019
TBD84 000:676.684 JLINK_Go()
TBD84 000:676.706   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:679.576 - 2.891ms
TBD84 000:679.603 JLINK_IsHalted()
TBD84 000:680.281 - 0.677ms returns FALSE
TBD84 000:680.308 JLINK_HasError()
TBD84 000:682.868 JLINK_IsHalted()
TBD84 000:685.101   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:685.795 - 2.925ms returns TRUE
TBD84 000:685.854 JLINK_ReadReg(R15 (PC))
TBD84 000:685.900 - 0.045ms returns 0x20200000
TBD84 000:685.946 JLINK_ClrBPEx(BPHandle = 0x00000019)
TBD84 000:685.990 - 0.043ms returns 0x00
TBD84 000:686.035 JLINK_ReadReg(R0)
TBD84 000:686.079 - 0.043ms returns 0x00000000
TBD84 000:686.645 JLINK_HasError()
TBD84 000:686.704 JLINK_WriteReg(R0, 0x00000001)
TBD84 000:686.749 - 0.045ms returns 0
TBD84 000:686.782 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:686.802 - 0.019ms returns 0
TBD84 000:686.822 JLINK_WriteReg(R2, 0x000000FF)
TBD84 000:686.842 - 0.019ms returns 0
TBD84 000:686.862 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:686.882 - 0.019ms returns 0
TBD84 000:686.902 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:686.922 - 0.019ms returns 0
TBD84 000:686.942 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:686.962 - 0.019ms returns 0
TBD84 000:686.982 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:687.002 - 0.019ms returns 0
TBD84 000:687.022 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:687.041 - 0.019ms returns 0
TBD84 000:687.062 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:687.082 - 0.019ms returns 0
TBD84 000:687.102 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:687.121 - 0.019ms returns 0
TBD84 000:687.142 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:687.161 - 0.019ms returns 0
TBD84 000:687.182 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:687.201 - 0.019ms returns 0
TBD84 000:687.222 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:687.241 - 0.019ms returns 0
TBD84 000:687.262 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:687.282 - 0.019ms returns 0
TBD84 000:687.302 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:687.322 - 0.019ms returns 0
TBD84 000:687.342 JLINK_WriteReg(R15 (PC), 0x20200094)
TBD84 000:687.362 - 0.019ms returns 0
TBD84 000:687.382 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:687.402 - 0.019ms returns 0
TBD84 000:687.422 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:687.442 - 0.019ms returns 0
TBD84 000:687.463 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:687.482 - 0.019ms returns 0
TBD84 000:687.503 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:687.522 - 0.019ms returns 0
TBD84 000:687.543 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:687.563 - 0.020ms returns 0x0000001A
TBD84 000:687.584 JLINK_Go()
TBD84 000:687.606   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:690.497 - 2.912ms
TBD84 000:690.563 JLINK_IsHalted()
TBD84 000:692.807   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:693.530 - 2.967ms returns TRUE
TBD84 000:693.560 JLINK_ReadReg(R15 (PC))
TBD84 000:693.581 - 0.020ms returns 0x20200000
TBD84 000:693.603 JLINK_ClrBPEx(BPHandle = 0x0000001A)
TBD84 000:693.625 - 0.022ms returns 0x00
TBD84 000:693.647 JLINK_ReadReg(R0)
TBD84 000:693.666 - 0.019ms returns 0x00000000
TBD84 000:751.323 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
TBD84 000:751.347   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TBD84 000:751.382   CPU_WriteMem(660 bytes @ 0x20200000)
TBD84 000:753.520 - 2.197ms returns 0x294
TBD84 000:753.616 JLINK_HasError()
TBD84 000:753.665 JLINK_WriteReg(R0, 0x00000000)
TBD84 000:753.712 - 0.046ms returns 0
TBD84 000:753.758 JLINK_WriteReg(R1, 0x01F78A40)
TBD84 000:753.802 - 0.044ms returns 0
TBD84 000:753.849 JLINK_WriteReg(R2, 0x00000002)
TBD84 000:753.897 - 0.047ms returns 0
TBD84 000:753.917 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:753.937 - 0.019ms returns 0
TBD84 000:753.957 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:753.977 - 0.019ms returns 0
TBD84 000:753.998 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:754.017 - 0.019ms returns 0
TBD84 000:754.038 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:754.057 - 0.019ms returns 0
TBD84 000:754.078 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:754.098 - 0.019ms returns 0
TBD84 000:754.118 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:754.138 - 0.019ms returns 0
TBD84 000:754.158 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:754.178 - 0.019ms returns 0
TBD84 000:754.198 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:754.218 - 0.019ms returns 0
TBD84 000:754.239 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:754.258 - 0.019ms returns 0
TBD84 000:754.279 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:754.298 - 0.019ms returns 0
TBD84 000:754.321 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:754.340 - 0.020ms returns 0
TBD84 000:754.361 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:754.380 - 0.019ms returns 0
TBD84 000:754.401 JLINK_WriteReg(R15 (PC), 0x20200038)
TBD84 000:754.421 - 0.019ms returns 0
TBD84 000:754.441 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:754.461 - 0.019ms returns 0
TBD84 000:754.481 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:754.501 - 0.019ms returns 0
TBD84 000:754.522 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:754.541 - 0.019ms returns 0
TBD84 000:754.562 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:754.581 - 0.019ms returns 0
TBD84 000:754.602 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:754.624   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 000:755.321   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 000:756.064   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 000:756.816   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 000:757.550   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:758.307 - 3.705ms returns 0x0000001B
TBD84 000:758.335 JLINK_Go()
TBD84 000:758.357   CPU_WriteMem(2 bytes @ 0x20200000)
TBD84 000:759.053   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:762.082 - 3.746ms
TBD84 000:762.110 JLINK_IsHalted()
TBD84 000:764.341   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:765.095 - 2.984ms returns TRUE
TBD84 000:765.123 JLINK_ReadReg(R15 (PC))
TBD84 000:765.144 - 0.021ms returns 0x20200000
TBD84 000:765.165 JLINK_ClrBPEx(BPHandle = 0x0000001B)
TBD84 000:765.185 - 0.019ms returns 0x00
TBD84 000:765.206 JLINK_ReadReg(R0)
TBD84 000:765.226 - 0.019ms returns 0x00000000
TBD84 000:765.478 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:765.502   Data:  50 0C 20 20 D5 00 00 00 D9 00 00 00 DB 00 00 00 ...
TBD84 000:765.535   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:767.172 - 1.694ms returns 0x16C
TBD84 000:767.201 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:767.221   Data:  C2 07 11 43 DA 05 80 1A 80 19 40 08 80 18 00 F0 ...
TBD84 000:767.285   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:769.454 - 2.253ms returns 0x294
TBD84 000:769.481 JLINK_HasError()
TBD84 000:769.503 JLINK_WriteReg(R0, 0x00000000)
TBD84 000:769.524 - 0.020ms returns 0
TBD84 000:769.545 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:769.564 - 0.019ms returns 0
TBD84 000:769.584 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:769.603 - 0.019ms returns 0
TBD84 000:769.633 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:769.652 - 0.019ms returns 0
TBD84 000:769.673 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:769.693 - 0.019ms returns 0
TBD84 000:769.714 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:769.733 - 0.019ms returns 0
TBD84 000:769.754 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:769.772 - 0.018ms returns 0
TBD84 000:769.793 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:769.812 - 0.019ms returns 0
TBD84 000:769.847 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:769.868 - 0.021ms returns 0
TBD84 000:769.889 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:769.908 - 0.019ms returns 0
TBD84 000:769.932 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:769.954 - 0.021ms returns 0
TBD84 000:769.974 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:769.994 - 0.019ms returns 0
TBD84 000:770.014 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:770.033 - 0.019ms returns 0
TBD84 000:770.055 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:770.074 - 0.019ms returns 0
TBD84 000:770.094 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:770.115 - 0.020ms returns 0
TBD84 000:770.135 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:770.154 - 0.019ms returns 0
TBD84 000:770.178 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:770.225 - 0.047ms returns 0
TBD84 000:770.246 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:770.266 - 0.019ms returns 0
TBD84 000:770.286 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:770.306 - 0.020ms returns 0
TBD84 000:770.327 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:770.346 - 0.019ms returns 0
TBD84 000:770.367 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:770.394 - 0.027ms returns 0x0000001C
TBD84 000:770.414 JLINK_Go()
TBD84 000:770.441   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:773.475 - 3.060ms
TBD84 000:773.506 JLINK_IsHalted()
TBD84 000:774.165 - 0.658ms returns FALSE
TBD84 000:774.198 JLINK_HasError()
TBD84 000:777.363 JLINK_IsHalted()
TBD84 000:778.049 - 0.686ms returns FALSE
TBD84 000:778.083 JLINK_HasError()
TBD84 000:779.536 JLINK_IsHalted()
TBD84 000:781.859   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:782.571 - 3.034ms returns TRUE
TBD84 000:782.645 JLINK_ReadReg(R15 (PC))
TBD84 000:782.703 - 0.058ms returns 0x20200000
TBD84 000:782.756 JLINK_ClrBPEx(BPHandle = 0x0000001C)
TBD84 000:782.806 - 0.050ms returns 0x00
TBD84 000:782.858 JLINK_ReadReg(R0)
TBD84 000:782.942 - 0.083ms returns 0x00000000
TBD84 000:783.383 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:783.409   Data:  0D 43 EA D1 10 46 18 43 13 D0 60 46 19 46 50 40 ...
TBD84 000:783.445   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:785.070 - 1.686ms returns 0x16C
TBD84 000:785.100 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:785.122   Data:  70 BD F0 B5 1F B4 06 46 00 20 82 B0 05 46 40 24 ...
TBD84 000:785.156   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:787.392 - 2.291ms returns 0x294
TBD84 000:787.424 JLINK_HasError()
TBD84 000:787.447 JLINK_WriteReg(R0, 0x00000400)
TBD84 000:787.470 - 0.022ms returns 0
TBD84 000:787.494 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:787.514 - 0.020ms returns 0
TBD84 000:787.538 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:787.565 - 0.026ms returns 0
TBD84 000:787.587 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:787.608 - 0.021ms returns 0
TBD84 000:787.633 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:787.654 - 0.021ms returns 0
TBD84 000:787.678 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:787.703 - 0.024ms returns 0
TBD84 000:787.724 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:787.746 - 0.021ms returns 0
TBD84 000:787.768 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:787.789 - 0.021ms returns 0
TBD84 000:787.817 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:787.840 - 0.023ms returns 0
TBD84 000:787.862 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:787.887 - 0.025ms returns 0
TBD84 000:787.909 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:787.934 - 0.024ms returns 0
TBD84 000:787.962 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:787.986 - 0.024ms returns 0
TBD84 000:788.038 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:788.069 - 0.030ms returns 0
TBD84 000:788.091 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:788.112 - 0.021ms returns 0
TBD84 000:788.139 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:788.163 - 0.023ms returns 0
TBD84 000:788.189 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:788.210 - 0.021ms returns 0
TBD84 000:788.248 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:788.274 - 0.026ms returns 0
TBD84 000:788.298 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:788.321 - 0.022ms returns 0
TBD84 000:788.351 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:788.372 - 0.021ms returns 0
TBD84 000:788.399 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:788.420 - 0.021ms returns 0
TBD84 000:788.444 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:788.467 - 0.023ms returns 0x0000001D
TBD84 000:788.490 JLINK_Go()
TBD84 000:788.518   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:791.597 - 3.106ms
TBD84 000:791.629 JLINK_IsHalted()
TBD84 000:792.285 - 0.656ms returns FALSE
TBD84 000:792.314 JLINK_HasError()
TBD84 000:794.266 JLINK_IsHalted()
TBD84 000:795.048 - 0.782ms returns FALSE
TBD84 000:795.110 JLINK_HasError()
TBD84 000:796.418 JLINK_IsHalted()
TBD84 000:797.170 - 0.751ms returns FALSE
TBD84 000:797.199 JLINK_HasError()
TBD84 000:798.747 JLINK_IsHalted()
TBD84 000:801.090   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:801.795 - 3.052ms returns TRUE
TBD84 000:801.866 JLINK_ReadReg(R15 (PC))
TBD84 000:801.918 - 0.052ms returns 0x20200000
TBD84 000:801.971 JLINK_ClrBPEx(BPHandle = 0x0000001D)
TBD84 000:802.016 - 0.044ms returns 0x00
TBD84 000:802.052 JLINK_ReadReg(R0)
TBD84 000:802.077 - 0.025ms returns 0x00000000
TBD84 000:802.541 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:802.566   Data:  D2 0F 05 9E D2 07 00 93 01 92 02 96 2A 46 23 46 ...
TBD84 000:802.600   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:804.159 - 1.618ms returns 0x16C
TBD84 000:804.188 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:804.208   Data:  04 DB 08 46 20 3A D0 40 00 21 10 BD 0B 46 D3 40 ...
TBD84 000:804.242   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:806.292 - 2.104ms returns 0x294
TBD84 000:806.320 JLINK_HasError()
TBD84 000:806.342 JLINK_WriteReg(R0, 0x00000800)
TBD84 000:806.362 - 0.020ms returns 0
TBD84 000:806.382 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:806.402 - 0.019ms returns 0
TBD84 000:806.422 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:806.445 - 0.022ms returns 0
TBD84 000:806.466 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:806.485 - 0.018ms returns 0
TBD84 000:806.505 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:806.524 - 0.019ms returns 0
TBD84 000:806.547 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:806.567 - 0.020ms returns 0
TBD84 000:806.588 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:806.610 - 0.021ms returns 0
TBD84 000:806.630 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:806.650 - 0.020ms returns 0
TBD84 000:806.682 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:806.702 - 0.020ms returns 0
TBD84 000:806.724 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:806.748 - 0.024ms returns 0
TBD84 000:806.777 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:806.818 - 0.041ms returns 0
TBD84 000:806.853 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:806.876 - 0.023ms returns 0
TBD84 000:806.898 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:806.925 - 0.027ms returns 0
TBD84 000:806.958 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:806.984 - 0.026ms returns 0
TBD84 000:807.012 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:807.036 - 0.024ms returns 0
TBD84 000:807.061 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:807.102 - 0.039ms returns 0
TBD84 000:807.126 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:807.149 - 0.023ms returns 0
TBD84 000:807.173 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:807.196 - 0.023ms returns 0
TBD84 000:807.218 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:807.250 - 0.031ms returns 0
TBD84 000:807.277 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:807.321 - 0.044ms returns 0
TBD84 000:807.350 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:807.372 - 0.021ms returns 0x0000001E
TBD84 000:807.398 JLINK_Go()
TBD84 000:807.426   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:810.342 - 2.944ms
TBD84 000:810.375 JLINK_IsHalted()
TBD84 000:811.034 - 0.658ms returns FALSE
TBD84 000:811.064 JLINK_HasError()
TBD84 000:812.877 JLINK_IsHalted()
TBD84 000:813.524 - 0.647ms returns FALSE
TBD84 000:813.552 JLINK_HasError()
TBD84 000:815.191 JLINK_IsHalted()
TBD84 000:815.915 - 0.724ms returns FALSE
TBD84 000:815.943 JLINK_HasError()
TBD84 000:817.498 JLINK_IsHalted()
TBD84 000:819.816   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:820.530 - 3.031ms returns TRUE
TBD84 000:820.563 JLINK_ReadReg(R15 (PC))
TBD84 000:820.609 - 0.045ms returns 0x20200000
TBD84 000:820.636 JLINK_ClrBPEx(BPHandle = 0x0000001E)
TBD84 000:820.658 - 0.021ms returns 0x00
TBD84 000:820.681 JLINK_ReadReg(R0)
TBD84 000:820.702 - 0.021ms returns 0x00000000
TBD84 000:821.200 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:821.228   Data:  90 12 00 00 81 B0 00 90 00 99 09 20 42 02 88 58 ...
TBD84 000:821.264   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:822.721 - 1.521ms returns 0x16C
TBD84 000:822.752 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:822.796   Data:  0D 68 6D 04 FC D5 45 7E 25 40 9E 69 A6 43 74 19 ...
TBD84 000:822.833   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:824.967 - 2.214ms returns 0x294
TBD84 000:824.996 JLINK_HasError()
TBD84 000:825.020 JLINK_WriteReg(R0, 0x00000C00)
TBD84 000:825.042 - 0.022ms returns 0
TBD84 000:825.068 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:825.088 - 0.020ms returns 0
TBD84 000:825.110 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:825.130 - 0.020ms returns 0
TBD84 000:825.151 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:825.193 - 0.042ms returns 0
TBD84 000:825.216 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:825.237 - 0.021ms returns 0
TBD84 000:825.263 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:825.286 - 0.022ms returns 0
TBD84 000:825.307 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:825.329 - 0.021ms returns 0
TBD84 000:825.371 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:825.397 - 0.026ms returns 0
TBD84 000:825.421 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:825.442 - 0.021ms returns 0
TBD84 000:825.463 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:825.485 - 0.022ms returns 0
TBD84 000:825.507 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:825.528 - 0.020ms returns 0
TBD84 000:825.573 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:825.598 - 0.024ms returns 0
TBD84 000:825.619 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:825.640 - 0.020ms returns 0
TBD84 000:825.661 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:825.682 - 0.020ms returns 0
TBD84 000:825.703 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:825.724 - 0.020ms returns 0
TBD84 000:825.747 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:825.767 - 0.020ms returns 0
TBD84 000:825.810 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:825.835 - 0.025ms returns 0
TBD84 000:825.857 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:825.883 - 0.025ms returns 0
TBD84 000:825.909 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:825.930 - 0.021ms returns 0
TBD84 000:825.957 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:826.005 - 0.047ms returns 0
TBD84 000:826.036 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:826.060 - 0.024ms returns 0x0000001F
TBD84 000:826.114 JLINK_Go()
TBD84 000:826.149   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:829.094 - 2.980ms
TBD84 000:829.128 JLINK_IsHalted()
TBD84 000:829.913 - 0.784ms returns FALSE
TBD84 000:829.945 JLINK_HasError()
TBD84 000:832.241 JLINK_IsHalted()
TBD84 000:833.041 - 0.800ms returns FALSE
TBD84 000:833.075 JLINK_HasError()
TBD84 000:834.343 JLINK_IsHalted()
TBD84 000:835.035 - 0.692ms returns FALSE
TBD84 000:835.078 JLINK_HasError()
TBD84 000:836.371 JLINK_IsHalted()
TBD84 000:838.719   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:839.426 - 3.055ms returns TRUE
TBD84 000:839.457 JLINK_ReadReg(R15 (PC))
TBD84 000:839.480 - 0.022ms returns 0x20200000
TBD84 000:839.502 JLINK_ClrBPEx(BPHandle = 0x0000001F)
TBD84 000:839.523 - 0.021ms returns 0x00
TBD84 000:839.546 JLINK_ReadReg(R0)
TBD84 000:839.567 - 0.021ms returns 0x00000000
TBD84 000:839.978 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:840.009   Data:  C9 01 0F 43 D7 66 17 46 70 37 3C 68 2C 40 0C 43 ...
TBD84 000:840.047   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:841.638 - 1.659ms returns 0x16C
TBD84 000:841.666 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:841.687   Data:  01 00 00 26 81 B0 00 90 00 98 81 21 49 01 40 58 ...
TBD84 000:841.720   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:843.903 - 2.236ms returns 0x294
TBD84 000:843.978 JLINK_HasError()
TBD84 000:844.027 JLINK_WriteReg(R0, 0x00001000)
TBD84 000:844.071 - 0.043ms returns 0
TBD84 000:844.092 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:844.113 - 0.020ms returns 0
TBD84 000:844.134 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:844.154 - 0.019ms returns 0
TBD84 000:844.203 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:844.225 - 0.021ms returns 0
TBD84 000:844.247 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:844.268 - 0.020ms returns 0
TBD84 000:844.289 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:844.310 - 0.020ms returns 0
TBD84 000:844.351 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:844.376 - 0.024ms returns 0
TBD84 000:844.397 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:844.418 - 0.020ms returns 0
TBD84 000:844.440 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:844.461 - 0.021ms returns 0
TBD84 000:844.494 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:844.519 - 0.024ms returns 0
TBD84 000:844.557 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:844.578 - 0.020ms returns 0
TBD84 000:844.599 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:844.619 - 0.020ms returns 0
TBD84 000:844.641 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:844.661 - 0.020ms returns 0
TBD84 000:844.682 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:844.703 - 0.020ms returns 0
TBD84 000:844.724 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:844.744 - 0.020ms returns 0
TBD84 000:844.766 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:844.786 - 0.020ms returns 0
TBD84 000:844.812 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:844.873 - 0.060ms returns 0
TBD84 000:844.897 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:844.917 - 0.020ms returns 0
TBD84 000:844.938 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:844.960 - 0.022ms returns 0
TBD84 000:845.010 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:845.032 - 0.021ms returns 0
TBD84 000:845.054 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:845.075 - 0.021ms returns 0x00000020
TBD84 000:845.101 JLINK_Go()
TBD84 000:845.132   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:847.977 - 2.875ms
TBD84 000:848.015 JLINK_IsHalted()
TBD84 000:848.658 - 0.643ms returns FALSE
TBD84 000:848.692 JLINK_HasError()
TBD84 000:850.778 JLINK_IsHalted()
TBD84 000:851.540 - 0.762ms returns FALSE
TBD84 000:851.571 JLINK_HasError()
TBD84 000:853.165 JLINK_IsHalted()
TBD84 000:854.020 - 0.853ms returns FALSE
TBD84 000:854.085 JLINK_HasError()
TBD84 000:855.879 JLINK_IsHalted()
TBD84 000:858.237   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:858.919 - 3.039ms returns TRUE
TBD84 000:858.947 JLINK_ReadReg(R15 (PC))
TBD84 000:858.968 - 0.020ms returns 0x20200000
TBD84 000:858.989 JLINK_ClrBPEx(BPHandle = 0x00000020)
TBD84 000:859.009 - 0.020ms returns 0x00
TBD84 000:859.031 JLINK_ReadReg(R0)
TBD84 000:859.050 - 0.019ms returns 0x00000000
TBD84 000:859.470 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:859.496   Data:  FF 20 08 70 80 E0 07 98 08 99 88 61 07 98 FF F7 ...
TBD84 000:859.539   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:861.157 - 1.687ms returns 0x16C
TBD84 000:861.192 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:861.217   Data:  00 90 1A 48 03 90 00 F0 92 FD 07 98 18 49 0C 69 ...
TBD84 000:861.254   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:863.379 - 2.185ms returns 0x294
TBD84 000:863.442 JLINK_HasError()
TBD84 000:863.493 JLINK_WriteReg(R0, 0x00001400)
TBD84 000:863.541 - 0.047ms returns 0
TBD84 000:863.589 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:863.636 - 0.047ms returns 0
TBD84 000:863.732 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:863.779 - 0.047ms returns 0
TBD84 000:863.829 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:863.885 - 0.056ms returns 0
TBD84 000:863.932 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:863.976 - 0.044ms returns 0
TBD84 000:864.025 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:864.125 - 0.099ms returns 0
TBD84 000:864.159 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:864.190 - 0.031ms returns 0
TBD84 000:864.214 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:864.235 - 0.021ms returns 0
TBD84 000:864.278 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:864.306 - 0.027ms returns 0
TBD84 000:864.327 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:864.352 - 0.025ms returns 0
TBD84 000:864.376 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:864.400 - 0.023ms returns 0
TBD84 000:864.422 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:864.442 - 0.020ms returns 0
TBD84 000:864.477 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:864.502 - 0.025ms returns 0
TBD84 000:864.526 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:864.548 - 0.022ms returns 0
TBD84 000:864.571 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:864.596 - 0.024ms returns 0
TBD84 000:864.619 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:864.662 - 0.042ms returns 0
TBD84 000:864.686 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:864.711 - 0.024ms returns 0
TBD84 000:864.738 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:864.758 - 0.020ms returns 0
TBD84 000:864.779 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:864.800 - 0.020ms returns 0
TBD84 000:864.822 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:864.857 - 0.034ms returns 0
TBD84 000:864.920 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:864.943 - 0.023ms returns 0x00000021
TBD84 000:864.964 JLINK_Go()
TBD84 000:864.992   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:867.891 - 2.926ms
TBD84 000:867.924 JLINK_IsHalted()
TBD84 000:868.651 - 0.727ms returns FALSE
TBD84 000:868.683 JLINK_HasError()
TBD84 000:870.293 JLINK_IsHalted()
TBD84 000:871.062 - 0.768ms returns FALSE
TBD84 000:871.124 JLINK_HasError()
TBD84 000:872.443 JLINK_IsHalted()
TBD84 000:873.193 - 0.750ms returns FALSE
TBD84 000:873.237 JLINK_HasError()
TBD84 000:874.535 JLINK_IsHalted()
TBD84 000:876.813   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:877.569 - 3.033ms returns TRUE
TBD84 000:877.635 JLINK_ReadReg(R15 (PC))
TBD84 000:877.693 - 0.057ms returns 0x20200000
TBD84 000:877.751 JLINK_ClrBPEx(BPHandle = 0x00000021)
TBD84 000:877.800 - 0.049ms returns 0x00
TBD84 000:877.852 JLINK_ReadReg(R0)
TBD84 000:877.903 - 0.050ms returns 0x00000000
TBD84 000:878.487 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:878.518   Data:  80 B5 82 B0 12 49 E9 48 01 90 FF F7 52 FA 01 98 ...
TBD84 000:878.559   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:880.173 - 1.686ms returns 0x16C
TBD84 000:880.208 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:880.233   Data:  FF F7 8A FA 06 48 FF F7 F1 F9 10 20 FF F7 BA FA ...
TBD84 000:880.272   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:882.465 - 2.256ms returns 0x294
TBD84 000:882.500 JLINK_HasError()
TBD84 000:882.527 JLINK_WriteReg(R0, 0x00001800)
TBD84 000:882.551 - 0.023ms returns 0
TBD84 000:882.579 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:882.605 - 0.025ms returns 0
TBD84 000:882.659 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:882.681 - 0.022ms returns 0
TBD84 000:882.705 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:882.725 - 0.020ms returns 0
TBD84 000:882.749 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:882.770 - 0.021ms returns 0
TBD84 000:882.794 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:882.848 - 0.053ms returns 0
TBD84 000:882.875 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:882.899 - 0.023ms returns 0
TBD84 000:882.923 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:882.946 - 0.022ms returns 0
TBD84 000:882.970 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:883.014 - 0.044ms returns 0
TBD84 000:883.044 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:883.065 - 0.020ms returns 0
TBD84 000:883.090 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:883.115 - 0.025ms returns 0
TBD84 000:883.141 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:883.166 - 0.025ms returns 0
TBD84 000:883.190 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:883.230 - 0.039ms returns 0
TBD84 000:883.263 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:883.287 - 0.023ms returns 0
TBD84 000:883.318 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:883.349 - 0.031ms returns 0
TBD84 000:883.374 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:883.397 - 0.023ms returns 0
TBD84 000:883.421 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:883.443 - 0.022ms returns 0
TBD84 000:883.466 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:883.492 - 0.026ms returns 0
TBD84 000:883.516 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:883.537 - 0.021ms returns 0
TBD84 000:883.566 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:883.601 - 0.035ms returns 0
TBD84 000:883.626 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:883.647 - 0.021ms returns 0x00000022
TBD84 000:883.672 JLINK_Go()
TBD84 000:883.695   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:886.588 - 2.915ms
TBD84 000:886.644 JLINK_IsHalted()
TBD84 000:887.305 - 0.660ms returns FALSE
TBD84 000:887.372 JLINK_HasError()
TBD84 000:889.248 JLINK_IsHalted()
TBD84 000:889.913 - 0.664ms returns FALSE
TBD84 000:889.945 JLINK_HasError()
TBD84 000:891.445 JLINK_IsHalted()
TBD84 000:892.164 - 0.718ms returns FALSE
TBD84 000:892.197 JLINK_HasError()
TBD84 000:893.522 JLINK_IsHalted()
TBD84 000:895.875   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:896.663 - 3.141ms returns TRUE
TBD84 000:896.694 JLINK_ReadReg(R15 (PC))
TBD84 000:896.719 - 0.024ms returns 0x20200000
TBD84 000:896.745 JLINK_ClrBPEx(BPHandle = 0x00000022)
TBD84 000:896.768 - 0.023ms returns 0x00
TBD84 000:896.792 JLINK_ReadReg(R0)
TBD84 000:896.817 - 0.024ms returns 0x00000000
TBD84 000:897.281 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:897.342   Data:  00 90 00 98 C0 08 7D 28 03 D3 FF E7 06 48 00 90 ...
TBD84 000:897.419   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:899.010 - 1.729ms returns 0x16C
TBD84 000:899.041 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:899.067   Data:  33 04 20 20 31 03 20 20 40 04 20 20 80 B5 0F 48 ...
TBD84 000:899.105   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:901.211 - 2.169ms returns 0x294
TBD84 000:901.242 JLINK_HasError()
TBD84 000:901.272 JLINK_WriteReg(R0, 0x00001C00)
TBD84 000:901.296 - 0.024ms returns 0
TBD84 000:901.320 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:901.341 - 0.021ms returns 0
TBD84 000:901.370 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:901.392 - 0.022ms returns 0
TBD84 000:901.418 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:901.440 - 0.022ms returns 0
TBD84 000:901.465 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:901.488 - 0.023ms returns 0
TBD84 000:901.516 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:901.538 - 0.021ms returns 0
TBD84 000:901.563 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:901.614 - 0.050ms returns 0
TBD84 000:901.638 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:901.660 - 0.021ms returns 0
TBD84 000:901.682 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:901.708 - 0.026ms returns 0
TBD84 000:901.734 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:901.754 - 0.020ms returns 0
TBD84 000:901.804 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:901.832 - 0.027ms returns 0
TBD84 000:901.858 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:901.879 - 0.021ms returns 0
TBD84 000:901.905 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:901.926 - 0.021ms returns 0
TBD84 000:901.972 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:902.002 - 0.029ms returns 0
TBD84 000:902.027 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:902.049 - 0.021ms returns 0
TBD84 000:902.071 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:902.092 - 0.020ms returns 0
TBD84 000:902.116 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:902.138 - 0.021ms returns 0
TBD84 000:902.163 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:902.184 - 0.021ms returns 0
TBD84 000:902.207 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:902.229 - 0.021ms returns 0
TBD84 000:902.253 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:902.278 - 0.024ms returns 0
TBD84 000:902.320 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:902.347 - 0.027ms returns 0x00000023
TBD84 000:902.373 JLINK_Go()
TBD84 000:902.397   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:905.462 - 3.088ms
TBD84 000:905.496 JLINK_IsHalted()
TBD84 000:906.168 - 0.671ms returns FALSE
TBD84 000:906.200 JLINK_HasError()
TBD84 000:908.077 JLINK_IsHalted()
TBD84 000:908.806 - 0.729ms returns FALSE
TBD84 000:908.868 JLINK_HasError()
TBD84 000:910.106 JLINK_IsHalted()
TBD84 000:910.795 - 0.689ms returns FALSE
TBD84 000:910.831 JLINK_HasError()
TBD84 000:912.432 JLINK_IsHalted()
TBD84 000:914.823   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:915.547 - 3.114ms returns TRUE
TBD84 000:915.583 JLINK_ReadReg(R15 (PC))
TBD84 000:915.606 - 0.023ms returns 0x20200000
TBD84 000:915.631 JLINK_ClrBPEx(BPHandle = 0x00000023)
TBD84 000:915.654 - 0.022ms returns 0x00
TBD84 000:915.680 JLINK_ReadReg(R0)
TBD84 000:915.701 - 0.021ms returns 0x00000000
TBD84 000:916.223 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:916.250   Data:  00 20 01 90 FF F7 54 FD 09 49 0A 48 00 F0 28 F8 ...
TBD84 000:916.284   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:917.914 - 1.690ms returns 0x16C
TBD84 000:917.976 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:918.022   Data:  30 02 20 20 48 04 20 20 40 00 20 20 80 B5 82 B0 ...
TBD84 000:918.097   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:920.310 - 2.333ms returns 0x294
TBD84 000:920.372 JLINK_HasError()
TBD84 000:920.421 JLINK_WriteReg(R0, 0x00002000)
TBD84 000:920.468 - 0.046ms returns 0
TBD84 000:920.515 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:920.560 - 0.045ms returns 0
TBD84 000:920.606 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:920.650 - 0.043ms returns 0
TBD84 000:920.697 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:920.741 - 0.044ms returns 0
TBD84 000:920.789 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:920.835 - 0.045ms returns 0
TBD84 000:920.884 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:920.928 - 0.043ms returns 0
TBD84 000:920.975 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:921.019 - 0.044ms returns 0
TBD84 000:921.066 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:921.110 - 0.044ms returns 0
TBD84 000:921.159 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:921.215 - 0.056ms returns 0
TBD84 000:921.255 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:921.276 - 0.021ms returns 0
TBD84 000:921.298 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:921.318 - 0.020ms returns 0
TBD84 000:921.340 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:921.360 - 0.020ms returns 0
TBD84 000:921.385 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:921.409 - 0.023ms returns 0
TBD84 000:921.431 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:921.452 - 0.020ms returns 0
TBD84 000:921.477 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:921.510 - 0.032ms returns 0
TBD84 000:921.560 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:921.588 - 0.027ms returns 0
TBD84 000:921.612 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:921.635 - 0.023ms returns 0
TBD84 000:921.659 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:921.689 - 0.029ms returns 0
TBD84 000:921.711 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:921.771 - 0.058ms returns 0
TBD84 000:921.817 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:921.839 - 0.022ms returns 0
TBD84 000:921.861 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:921.883 - 0.022ms returns 0x00000024
TBD84 000:921.905 JLINK_Go()
TBD84 000:921.934   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:924.970 - 3.064ms
TBD84 000:925.036 JLINK_IsHalted()
TBD84 000:925.805 - 0.768ms returns FALSE
TBD84 000:925.845 JLINK_HasError()
TBD84 000:928.243 JLINK_IsHalted()
TBD84 000:928.908 - 0.665ms returns FALSE
TBD84 000:928.938 JLINK_HasError()
TBD84 000:930.870 JLINK_IsHalted()
TBD84 000:933.088   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:933.811 - 2.941ms returns TRUE
TBD84 000:933.840 JLINK_ReadReg(R15 (PC))
TBD84 000:933.861 - 0.021ms returns 0x20200000
TBD84 000:933.882 JLINK_ClrBPEx(BPHandle = 0x00000024)
TBD84 000:933.902 - 0.019ms returns 0x00
TBD84 000:933.922 JLINK_ReadReg(R0)
TBD84 000:933.944 - 0.021ms returns 0x00000000
TBD84 000:934.686 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:934.744   Data:  00 00 14 40 00 00 F0 3F 30 00 00 00 00 00 F0 43 ...
TBD84 000:934.817   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:936.418 - 1.732ms returns 0x16C
TBD84 000:936.447 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:936.469   Data:  18 98 EA 17 01 68 0D 60 4A 60 F6 E7 18 98 01 68 ...
TBD84 000:936.504   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:938.597 - 2.149ms returns 0x294
TBD84 000:938.632 JLINK_HasError()
TBD84 000:938.657 JLINK_WriteReg(R0, 0x00002400)
TBD84 000:938.680 - 0.023ms returns 0
TBD84 000:938.706 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:938.726 - 0.020ms returns 0
TBD84 000:938.749 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:938.772 - 0.023ms returns 0
TBD84 000:938.859 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:938.911 - 0.052ms returns 0
TBD84 000:938.968 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:939.016 - 0.048ms returns 0
TBD84 000:939.067 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:939.115 - 0.048ms returns 0
TBD84 000:939.163 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:939.218 - 0.055ms returns 0
TBD84 000:939.271 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:939.293 - 0.023ms returns 0
TBD84 000:939.318 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:939.339 - 0.021ms returns 0
TBD84 000:939.361 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:939.411 - 0.049ms returns 0
TBD84 000:939.439 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:939.462 - 0.024ms returns 0
TBD84 000:939.484 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:939.504 - 0.019ms returns 0
TBD84 000:939.525 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:939.548 - 0.023ms returns 0
TBD84 000:939.591 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:939.616 - 0.025ms returns 0
TBD84 000:939.640 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:939.664 - 0.024ms returns 0
TBD84 000:939.689 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:939.711 - 0.021ms returns 0
TBD84 000:939.733 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:939.797 - 0.064ms returns 0
TBD84 000:939.824 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:939.845 - 0.021ms returns 0
TBD84 000:939.868 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:939.893 - 0.025ms returns 0
TBD84 000:939.920 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:939.948 - 0.028ms returns 0
TBD84 000:939.972 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:939.993 - 0.021ms returns 0x00000025
TBD84 000:940.018 JLINK_Go()
TBD84 000:940.043   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:942.966 - 2.947ms
TBD84 000:943.002 JLINK_IsHalted()
TBD84 000:943.654 - 0.652ms returns FALSE
TBD84 000:943.689 JLINK_HasError()
TBD84 000:946.598 JLINK_IsHalted()
TBD84 000:947.303 - 0.704ms returns FALSE
TBD84 000:947.351 JLINK_HasError()
TBD84 000:949.105 JLINK_IsHalted()
TBD84 000:951.325   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:952.044 - 2.938ms returns TRUE
TBD84 000:952.075 JLINK_ReadReg(R15 (PC))
TBD84 000:952.102 - 0.026ms returns 0x20200000
TBD84 000:952.125 JLINK_ClrBPEx(BPHandle = 0x00000025)
TBD84 000:952.148 - 0.023ms returns 0x00
TBD84 000:952.172 JLINK_ReadReg(R0)
TBD84 000:952.194 - 0.022ms returns 0x00000000
TBD84 000:952.904 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:952.958   Data:  0D 99 00 78 49 1C 0D 91 1A 9A 19 99 90 47 6D 1C ...
TBD84 000:953.025   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:954.722 - 1.818ms returns 0x16C
TBD84 000:954.751 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:954.772   Data:  00 E0 01 91 01 29 04 DD 15 9A 49 1E 51 5C 30 29 ...
TBD84 000:954.804   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:956.958 - 2.206ms returns 0x294
TBD84 000:956.987 JLINK_HasError()
TBD84 000:957.010 JLINK_WriteReg(R0, 0x00002800)
TBD84 000:957.032 - 0.021ms returns 0
TBD84 000:957.053 JLINK_WriteReg(R1, 0x00000400)
TBD84 000:957.074 - 0.021ms returns 0
TBD84 000:957.100 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:957.120 - 0.020ms returns 0
TBD84 000:957.141 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:957.163 - 0.021ms returns 0
TBD84 000:957.188 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:957.208 - 0.020ms returns 0
TBD84 000:957.230 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:957.251 - 0.020ms returns 0
TBD84 000:957.272 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:957.292 - 0.020ms returns 0
TBD84 000:957.313 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:957.333 - 0.019ms returns 0
TBD84 000:957.354 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:957.402 - 0.047ms returns 0
TBD84 000:957.428 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:957.449 - 0.020ms returns 0
TBD84 000:957.470 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:957.490 - 0.020ms returns 0
TBD84 000:957.513 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:957.534 - 0.020ms returns 0
TBD84 000:957.555 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:957.575 - 0.020ms returns 0
TBD84 000:957.598 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:957.619 - 0.020ms returns 0
TBD84 000:957.640 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:957.661 - 0.021ms returns 0
TBD84 000:957.684 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:957.709 - 0.025ms returns 0
TBD84 000:957.762 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:957.785 - 0.023ms returns 0
TBD84 000:957.812 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:957.832 - 0.020ms returns 0
TBD84 000:957.854 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:957.874 - 0.021ms returns 0
TBD84 000:957.895 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:957.916 - 0.020ms returns 0
TBD84 000:957.942 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:957.963 - 0.021ms returns 0x00000026
TBD84 000:957.988 JLINK_Go()
TBD84 000:958.015   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:960.919 - 2.930ms
TBD84 000:960.951 JLINK_IsHalted()
TBD84 000:961.662 - 0.710ms returns FALSE
TBD84 000:961.700 JLINK_HasError()
TBD84 000:964.544 JLINK_IsHalted()
TBD84 000:965.289 - 0.745ms returns FALSE
TBD84 000:965.335 JLINK_HasError()
TBD84 000:967.705 JLINK_IsHalted()
TBD84 000:970.090   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:970.819 - 3.114ms returns TRUE
TBD84 000:970.857 JLINK_ReadReg(R15 (PC))
TBD84 000:970.880 - 0.023ms returns 0x20200000
TBD84 000:970.930 JLINK_ClrBPEx(BPHandle = 0x00000026)
TBD84 000:970.960 - 0.029ms returns 0x00
TBD84 000:970.985 JLINK_ReadReg(R0)
TBD84 000:971.008 - 0.022ms returns 0x00000000
TBD84 000:971.715 JLINK_WriteMem(0x20200294, 0x16C Bytes, ...)
TBD84 000:971.742   Data:  00 00 00 00 00 00 00 00 10 00 00 00 00 01 00 00 ...
TBD84 000:971.776   CPU_WriteMem(364 bytes @ 0x20200294)
TBD84 000:973.413 - 1.697ms returns 0x16C
TBD84 000:973.477 JLINK_WriteMem(0x20200400, 0x294 Bytes, ...)
TBD84 000:973.524   Data:  FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF FF ...
TBD84 000:973.597   CPU_WriteMem(660 bytes @ 0x20200400)
TBD84 000:975.761 - 2.283ms returns 0x294
TBD84 000:975.824 JLINK_HasError()
TBD84 000:975.875 JLINK_WriteReg(R0, 0x00002C00)
TBD84 000:975.923 - 0.048ms returns 0
TBD84 000:975.971 JLINK_WriteReg(R1, 0x000000C8)
TBD84 000:976.015 - 0.044ms returns 0
TBD84 000:976.062 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:976.111 - 0.049ms returns 0
TBD84 000:976.159 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:976.205 - 0.046ms returns 0
TBD84 000:976.254 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:976.298 - 0.044ms returns 0
TBD84 000:976.345 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:976.370 - 0.025ms returns 0
TBD84 000:976.427 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:976.453 - 0.026ms returns 0
TBD84 000:976.475 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:976.496 - 0.020ms returns 0
TBD84 000:976.517 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:976.537 - 0.020ms returns 0
TBD84 000:976.558 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:976.578 - 0.020ms returns 0
TBD84 000:976.599 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:976.620 - 0.020ms returns 0
TBD84 000:976.642 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:976.663 - 0.020ms returns 0
TBD84 000:976.685 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:976.708 - 0.022ms returns 0
TBD84 000:976.746 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:976.773 - 0.027ms returns 0
TBD84 000:976.799 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:976.823 - 0.024ms returns 0
TBD84 000:976.846 JLINK_WriteReg(R15 (PC), 0x20200120)
TBD84 000:976.868 - 0.022ms returns 0
TBD84 000:976.930 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:976.954 - 0.023ms returns 0
TBD84 000:976.975 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:976.997 - 0.021ms returns 0
TBD84 000:977.019 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:977.100 - 0.079ms returns 0
TBD84 000:977.157 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:977.206 - 0.049ms returns 0
TBD84 000:977.300 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:977.353 - 0.054ms returns 0x00000027
TBD84 000:977.380 JLINK_Go()
TBD84 000:977.423   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:980.470 - 3.089ms
TBD84 000:980.499 JLINK_IsHalted()
TBD84 000:981.168 - 0.668ms returns FALSE
TBD84 000:981.196 JLINK_HasError()
TBD84 000:983.527 JLINK_IsHalted()
TBD84 000:985.970   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:986.712 - 3.185ms returns TRUE
TBD84 000:986.741 JLINK_ReadReg(R15 (PC))
TBD84 000:986.762 - 0.021ms returns 0x20200000
TBD84 000:986.783 JLINK_ClrBPEx(BPHandle = 0x00000027)
TBD84 000:986.804 - 0.020ms returns 0x00
TBD84 000:986.826 JLINK_ReadReg(R0)
TBD84 000:986.846 - 0.020ms returns 0x00000000
TBD84 000:986.868 JLINK_HasError()
TBD84 000:986.889 JLINK_WriteReg(R0, 0x00000002)
TBD84 000:986.909 - 0.020ms returns 0
TBD84 000:986.931 JLINK_WriteReg(R1, 0x000000C8)
TBD84 000:986.957 - 0.026ms returns 0
TBD84 000:986.978 JLINK_WriteReg(R2, 0x20200294)
TBD84 000:986.999 - 0.021ms returns 0
TBD84 000:987.021 JLINK_WriteReg(R3, 0x00000000)
TBD84 000:987.041 - 0.020ms returns 0
TBD84 000:987.061 JLINK_WriteReg(R4, 0x00000000)
TBD84 000:987.082 - 0.020ms returns 0
TBD84 000:987.136 JLINK_WriteReg(R5, 0x00000000)
TBD84 000:987.194 - 0.057ms returns 0
TBD84 000:987.242 JLINK_WriteReg(R6, 0x00000000)
TBD84 000:987.289 - 0.046ms returns 0
TBD84 000:987.338 JLINK_WriteReg(R7, 0x00000000)
TBD84 000:987.377 - 0.040ms returns 0
TBD84 000:987.399 JLINK_WriteReg(R8, 0x00000000)
TBD84 000:987.429 - 0.029ms returns 0
TBD84 000:987.450 JLINK_WriteReg(R9, 0x20200290)
TBD84 000:987.474 - 0.024ms returns 0
TBD84 000:987.495 JLINK_WriteReg(R10, 0x00000000)
TBD84 000:987.514 - 0.019ms returns 0
TBD84 000:987.535 JLINK_WriteReg(R11, 0x00000000)
TBD84 000:987.555 - 0.019ms returns 0
TBD84 000:987.576 JLINK_WriteReg(R12, 0x00000000)
TBD84 000:987.598 - 0.021ms returns 0
TBD84 000:987.620 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 000:987.686 - 0.064ms returns 0
TBD84 000:987.736 JLINK_WriteReg(R14, 0x20200001)
TBD84 000:987.782 - 0.046ms returns 0
TBD84 000:987.830 JLINK_WriteReg(R15 (PC), 0x20200094)
TBD84 000:987.874 - 0.044ms returns 0
TBD84 000:987.921 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 000:987.969 - 0.047ms returns 0
TBD84 000:988.017 JLINK_WriteReg(MSP, 0x20208000)
TBD84 000:988.072 - 0.054ms returns 0
TBD84 000:988.094 JLINK_WriteReg(PSP, 0x20208000)
TBD84 000:988.116 - 0.021ms returns 0
TBD84 000:988.138 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 000:988.159 - 0.020ms returns 0
TBD84 000:988.194 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 000:988.220 - 0.026ms returns 0x00000028
TBD84 000:988.241 JLINK_Go()
TBD84 000:988.265   CPU_ReadMem(4 bytes @ 0x********)
TBD84 000:991.218 - 2.976ms
TBD84 000:991.275 JLINK_IsHalted()
TBD84 000:993.695   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 000:994.438 - 3.162ms returns TRUE
TBD84 000:994.496 JLINK_ReadReg(R15 (PC))
TBD84 000:994.542 - 0.046ms returns 0x20200000
TBD84 000:994.586 JLINK_ClrBPEx(BPHandle = 0x00000028)
TBD84 000:994.629 - 0.043ms returns 0x00
TBD84 000:994.674 JLINK_ReadReg(R0)
TBD84 000:994.731 - 0.056ms returns 0x00000000
TBD84 001:054.296 JLINK_WriteMem(0x20200000, 0x294 Bytes, ...)
TBD84 001:054.322   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
TBD84 001:054.364   CPU_WriteMem(660 bytes @ 0x20200000)
TBD84 001:056.510 - 2.214ms returns 0x294
TBD84 001:056.559 JLINK_HasError()
TBD84 001:056.587 JLINK_WriteReg(R0, 0x00000000)
TBD84 001:056.614 - 0.027ms returns 0
TBD84 001:056.638 JLINK_WriteReg(R1, 0x01F78A40)
TBD84 001:056.660 - 0.022ms returns 0
TBD84 001:056.684 JLINK_WriteReg(R2, 0x00000003)
TBD84 001:056.706 - 0.022ms returns 0
TBD84 001:056.730 JLINK_WriteReg(R3, 0x00000000)
TBD84 001:056.751 - 0.021ms returns 0
TBD84 001:056.774 JLINK_WriteReg(R4, 0x00000000)
TBD84 001:056.796 - 0.022ms returns 0
TBD84 001:056.818 JLINK_WriteReg(R5, 0x00000000)
TBD84 001:056.840 - 0.021ms returns 0
TBD84 001:056.862 JLINK_WriteReg(R6, 0x00000000)
TBD84 001:056.884 - 0.021ms returns 0
TBD84 001:056.906 JLINK_WriteReg(R7, 0x00000000)
TBD84 001:056.929 - 0.022ms returns 0
TBD84 001:056.951 JLINK_WriteReg(R8, 0x00000000)
TBD84 001:056.972 - 0.021ms returns 0
TBD84 001:057.036 JLINK_WriteReg(R9, 0x20200290)
TBD84 001:057.058 - 0.021ms returns 0
TBD84 001:057.081 JLINK_WriteReg(R10, 0x00000000)
TBD84 001:057.103 - 0.021ms returns 0
TBD84 001:057.139 JLINK_WriteReg(R11, 0x00000000)
TBD84 001:057.166 - 0.027ms returns 0
TBD84 001:057.189 JLINK_WriteReg(R12, 0x00000000)
TBD84 001:057.210 - 0.021ms returns 0
TBD84 001:057.233 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 001:057.262 - 0.029ms returns 0
TBD84 001:057.285 JLINK_WriteReg(R14, 0x20200001)
TBD84 001:057.307 - 0.022ms returns 0
TBD84 001:057.331 JLINK_WriteReg(R15 (PC), 0x20200038)
TBD84 001:057.353 - 0.022ms returns 0
TBD84 001:057.379 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 001:057.401 - 0.022ms returns 0
TBD84 001:057.425 JLINK_WriteReg(MSP, 0x20208000)
TBD84 001:057.448 - 0.022ms returns 0
TBD84 001:057.509 JLINK_WriteReg(PSP, 0x20208000)
TBD84 001:057.533 - 0.024ms returns 0
TBD84 001:057.557 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 001:057.580 - 0.022ms returns 0
TBD84 001:057.605 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 001:057.632   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 001:058.442   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 001:059.188   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 001:059.931   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 001:060.705   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 001:061.445 - 3.840ms returns 0x00000029
TBD84 001:061.476 JLINK_Go()
TBD84 001:061.532   CPU_WriteMem(2 bytes @ 0x20200000)
TBD84 001:062.301   CPU_ReadMem(4 bytes @ 0x********)
TBD84 001:065.213 - 3.736ms
TBD84 001:065.253 JLINK_IsHalted()
TBD84 001:067.446   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 001:068.196 - 2.942ms returns TRUE
TBD84 001:068.234 JLINK_ReadReg(R15 (PC))
TBD84 001:068.261 - 0.027ms returns 0x20200000
TBD84 001:068.289 JLINK_ClrBPEx(BPHandle = 0x00000029)
TBD84 001:068.311 - 0.021ms returns 0x00
TBD84 001:068.335 JLINK_ReadReg(R0)
TBD84 001:068.359 - 0.023ms returns 0x00000000
TBD84 001:068.383 JLINK_HasError()
TBD84 001:068.410 JLINK_WriteReg(R0, 0xFFFFFFFF)
TBD84 001:068.440 - 0.030ms returns 0
TBD84 001:068.464 JLINK_WriteReg(R1, 0x00000000)
TBD84 001:068.487 - 0.023ms returns 0
TBD84 001:068.517 JLINK_WriteReg(R2, 0x00002CC8)
TBD84 001:068.542 - 0.025ms returns 0
TBD84 001:068.569 JLINK_WriteReg(R3, 0x04C11DB7)
TBD84 001:068.592 - 0.023ms returns 0
TBD84 001:068.617 JLINK_WriteReg(R4, 0x00000000)
TBD84 001:068.640 - 0.023ms returns 0
TBD84 001:068.669 JLINK_WriteReg(R5, 0x00000000)
TBD84 001:068.697 - 0.028ms returns 0
TBD84 001:068.726 JLINK_WriteReg(R6, 0x00000000)
TBD84 001:068.750 - 0.024ms returns 0
TBD84 001:068.775 JLINK_WriteReg(R7, 0x00000000)
TBD84 001:068.836 - 0.060ms returns 0
TBD84 001:068.863 JLINK_WriteReg(R8, 0x00000000)
TBD84 001:068.895 - 0.031ms returns 0
TBD84 001:068.921 JLINK_WriteReg(R9, 0x20200290)
TBD84 001:068.944 - 0.023ms returns 0
TBD84 001:068.968 JLINK_WriteReg(R10, 0x00000000)
TBD84 001:068.992 - 0.023ms returns 0
TBD84 001:069.045 JLINK_WriteReg(R11, 0x00000000)
TBD84 001:069.079 - 0.034ms returns 0
TBD84 001:069.108 JLINK_WriteReg(R12, 0x00000000)
TBD84 001:069.134 - 0.025ms returns 0
TBD84 001:069.158 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 001:069.184 - 0.026ms returns 0
TBD84 001:069.210 JLINK_WriteReg(R14, 0x20200001)
TBD84 001:069.235 - 0.024ms returns 0
TBD84 001:069.260 JLINK_WriteReg(R15 (PC), 0x20200002)
TBD84 001:069.284 - 0.024ms returns 0
TBD84 001:069.307 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 001:069.332 - 0.024ms returns 0
TBD84 001:069.357 JLINK_WriteReg(MSP, 0x20208000)
TBD84 001:069.379 - 0.022ms returns 0
TBD84 001:069.419 JLINK_WriteReg(PSP, 0x20208000)
TBD84 001:069.445 - 0.025ms returns 0
TBD84 001:069.471 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 001:069.494 - 0.023ms returns 0
TBD84 001:069.523 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 001:069.550 - 0.027ms returns 0x0000002A
TBD84 001:069.576 JLINK_Go()
TBD84 001:069.606   CPU_ReadMem(4 bytes @ 0x********)
TBD84 001:072.525 - 2.948ms
TBD84 001:072.631 JLINK_IsHalted()
TBD84 001:073.284 - 0.653ms returns FALSE
TBD84 001:073.320 JLINK_HasError()
TBD84 001:076.276 JLINK_IsHalted()
TBD84 001:077.055 - 0.779ms returns FALSE
TBD84 001:077.088 JLINK_HasError()
TBD84 001:080.496 JLINK_IsHalted()
TBD84 001:081.310 - 0.813ms returns FALSE
TBD84 001:081.342 JLINK_HasError()
TBD84 001:082.479 JLINK_IsHalted()
TBD84 001:083.161 - 0.682ms returns FALSE
TBD84 001:083.205 JLINK_HasError()
TBD84 001:084.503 JLINK_IsHalted()
TBD84 001:085.290 - 0.786ms returns FALSE
TBD84 001:085.333 JLINK_HasError()
TBD84 001:088.496 JLINK_IsHalted()
TBD84 001:089.166 - 0.670ms returns FALSE
TBD84 001:089.197 JLINK_HasError()
TBD84 001:090.598 JLINK_IsHalted()
TBD84 001:091.317 - 0.717ms returns FALSE
TBD84 001:091.379 JLINK_HasError()
TBD84 001:092.593 JLINK_IsHalted()
TBD84 001:094.988   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 001:095.802 - 3.208ms returns TRUE
TBD84 001:095.830 JLINK_ReadReg(R15 (PC))
TBD84 001:095.852 - 0.021ms returns 0x20200000
TBD84 001:095.872 JLINK_ClrBPEx(BPHandle = 0x0000002A)
TBD84 001:095.891 - 0.019ms returns 0x00
TBD84 001:095.912 JLINK_ReadReg(R0)
TBD84 001:095.931 - 0.019ms returns 0xEB10569C
TBD84 001:096.648 JLINK_HasError()
TBD84 001:096.684 JLINK_WriteReg(R0, 0x00000003)
TBD84 001:096.705 - 0.020ms returns 0
TBD84 001:096.726 JLINK_WriteReg(R1, 0x00000000)
TBD84 001:096.746 - 0.020ms returns 0
TBD84 001:096.767 JLINK_WriteReg(R2, 0x00002CC8)
TBD84 001:096.786 - 0.019ms returns 0
TBD84 001:096.806 JLINK_WriteReg(R3, 0x04C11DB7)
TBD84 001:096.826 - 0.020ms returns 0
TBD84 001:096.847 JLINK_WriteReg(R4, 0x00000000)
TBD84 001:096.867 - 0.019ms returns 0
TBD84 001:096.888 JLINK_WriteReg(R5, 0x00000000)
TBD84 001:096.928 - 0.039ms returns 0
TBD84 001:096.952 JLINK_WriteReg(R6, 0x00000000)
TBD84 001:096.972 - 0.019ms returns 0
TBD84 001:096.995 JLINK_WriteReg(R7, 0x00000000)
TBD84 001:097.015 - 0.019ms returns 0
TBD84 001:097.036 JLINK_WriteReg(R8, 0x00000000)
TBD84 001:097.059 - 0.023ms returns 0
TBD84 001:097.080 JLINK_WriteReg(R9, 0x20200290)
TBD84 001:097.100 - 0.019ms returns 0
TBD84 001:097.122 JLINK_WriteReg(R10, 0x00000000)
TBD84 001:097.142 - 0.019ms returns 0
TBD84 001:097.163 JLINK_WriteReg(R11, 0x00000000)
TBD84 001:097.183 - 0.019ms returns 0
TBD84 001:097.229 JLINK_WriteReg(R12, 0x00000000)
TBD84 001:097.250 - 0.021ms returns 0
TBD84 001:097.271 JLINK_WriteReg(R13 (SP), 0x20208000)
TBD84 001:097.291 - 0.020ms returns 0
TBD84 001:097.311 JLINK_WriteReg(R14, 0x20200001)
TBD84 001:097.332 - 0.020ms returns 0
TBD84 001:097.366 JLINK_WriteReg(R15 (PC), 0x20200094)
TBD84 001:097.388 - 0.022ms returns 0
TBD84 001:097.409 JLINK_WriteReg(XPSR, 0x01000000)
TBD84 001:097.434 - 0.024ms returns 0
TBD84 001:097.454 JLINK_WriteReg(MSP, 0x20208000)
TBD84 001:097.474 - 0.019ms returns 0
TBD84 001:097.498 JLINK_WriteReg(PSP, 0x20208000)
TBD84 001:097.525 - 0.027ms returns 0
TBD84 001:097.574 JLINK_WriteReg(CFBP, 0x00000000)
TBD84 001:097.599 - 0.025ms returns 0
TBD84 001:097.627 JLINK_SetBPEx(Addr = 0x20200000, Type = 0xFFFFFFF2)
TBD84 001:097.649 - 0.022ms returns 0x0000002B
TBD84 001:097.702 JLINK_Go()
TBD84 001:097.732   CPU_ReadMem(4 bytes @ 0x********)
TBD84 001:100.600 - 2.896ms
TBD84 001:100.638 JLINK_IsHalted()
TBD84 001:102.804   CPU_ReadMem(2 bytes @ 0x20200000)
TBD84 001:103.545 - 2.906ms returns TRUE
TBD84 001:103.586 JLINK_ReadReg(R15 (PC))
TBD84 001:103.610 - 0.023ms returns 0x20200000
TBD84 001:103.634 JLINK_ClrBPEx(BPHandle = 0x0000002B)
TBD84 001:103.661 - 0.027ms returns 0x00
TBD84 001:103.686 JLINK_ReadReg(R0)
TBD84 001:103.723 - 0.037ms returns 0x00000000
TBD84 001:158.064 JLINK_WriteMemEx(0x20200000, 0x00000002 Bytes, Flags = 0x02000000)
TBD84 001:158.095   Data:  FE E7
TBD84 001:158.135   CPU_WriteMem(2 bytes @ 0x20200000)
TBD84 001:158.927 - 0.863ms returns 0x2
TBD84 001:158.957 JLINK_HasError()
TBD84 001:158.978 JLINK_HasError()
TBD84 001:158.999 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
TBD84 001:159.019 - 0.019ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
TBD84 001:159.040 JLINK_Reset()
TBD84 001:159.062   CPU_ReadMem(4 bytes @ 0x20200000)
TBD84 001:159.832   CPU_WriteMem(4 bytes @ 0x20200000)
TBD84 001:162.541   Memory map 'before startup completion point' is active
TBD84 001:162.581   CPU_WriteMem(4 bytes @ 0xE000EDF0)
TBD84 001:163.312   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TBD84 001:165.900   Reset: Halt core after reset via DEMCR.VC_CORERESET.
TBD84 001:168.873   Reset: Reset device via AIRCR.SYSRESETREQ.
TBD84 001:168.910   CPU_WriteMem(4 bytes @ 0xE000ED0C)
TBD84 001:224.973   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 001:225.817   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 001:226.585   CPU_WriteMem(4 bytes @ 0xE000EDFC)
TBD84 001:233.301   CPU_ReadMem(4 bytes @ 0xE000EDF0)
TBD84 001:236.404   CPU_WriteMem(4 bytes @ 0x********)
TBD84 001:237.180   CPU_ReadMem(4 bytes @ 0xE000EDFC)
TBD84 001:237.927   CPU_ReadMem(4 bytes @ 0x********)
TBD84 001:238.665   CPU_WriteMem(4 bytes @ 0x********)
TBD84 001:239.436 - 80.396ms
TBD84 001:239.469 JLINK_Go()
TBD84 001:239.495   CPU_ReadMem(4 bytes @ 0x********)
TBD84 001:240.171   CPU_WriteMem(4 bytes @ 0x********)
TBD84 001:240.941   CPU_WriteMem(4 bytes @ 0xE0002008)
TBD84 001:241.013   CPU_WriteMem(4 bytes @ 0xE000200C)
TBD84 001:241.073   CPU_WriteMem(4 bytes @ 0xE0002010)
TBD84 001:241.133   CPU_WriteMem(4 bytes @ 0xE0002014)
TBD84 001:242.341   CPU_WriteMem(4 bytes @ 0xE0001004)
TBD84 001:245.866   Memory map 'after startup completion point' is active
TBD84 001:245.902 - 6.432ms
TBD84 001:253.483 JLINK_Close()
TBD84 001:254.357   CPU is running
TBD84 001:254.394   CPU_WriteMem(4 bytes @ 0xE0002008)
TBD84 001:255.077   CPU is running
TBD84 001:255.111   CPU_WriteMem(4 bytes @ 0xE000200C)
TBD84 001:255.805   CPU is running
TBD84 001:255.839   CPU_WriteMem(4 bytes @ 0xE0002010)
TBD84 001:256.583   CPU is running
TBD84 001:256.663   CPU_WriteMem(4 bytes @ 0xE0002014)
TBD84 001:275.106 - 21.623ms
TBD84 001:275.139   
TBD84 001:275.166   Closed
