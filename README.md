# MSPM0G3507 智能小车项目引脚配置文档

## 项目概述
本项目基于德州仪器(TI) MSPM0G3507微控制器开发的智能小车系统，具备双电机驱动、编码器反馈、灰度传感器循迹、IMU姿态检测等功能。

## 芯片信息
- **芯片型号**: MSPM0G3507
- **架构**: ARM Cortex-M0+
- **主频**: 80MHz
- **封装**: 64-pin

## 引脚配置详表

### 1. 时钟系统
| 功能 | 引脚 | GPIO | 包装引脚 | 说明 |
|------|------|------|----------|------|
| HFXIN | PA5 | GPIOA.5 | 10 | 40MHz外部晶振输入 |
| HFXOUT | PA6 | GPIOA.6 | 11 | 40MHz外部晶振输出 |

### 2. 调试接口
| 功能 | 引脚 | GPIO | 包装引脚 | 说明 |
|------|------|------|----------|------|
| SWCLK | PA20 | GPIOA.20 | 43 | SWD时钟线 |
| SWDIO | PA19 | GPIOA.19 | 42 | SWD数据线 |

### 3. 电机控制系统

#### 3.1 PWM输出
| 功能 | 引脚 | GPIO | 包装引脚 | 定时器 | 通道 | 说明 |
|------|------|------|----------|--------|------|------|
| 左电机PWM（A） | PB11 | GPIOB.11 | 28 | TIMG8 | CCP1 | 左轮速度控制 |
| 右电机PWM（B） | PA27 | GPIOA.27 | 60 | TIMG7 | CCP1 | 右轮速度控制 |

#### 3.2 电机方向控制
| 功能 | 引脚 | GPIO | 包装引脚 | 说明 |
|------|------|------|----------|------|
| 左电机DIR1 | PA22 | GPIOA.22 | 47 | 左电机方向控制1(AIN1) |
| 左电机DIR2 | PB24 | GPIOB.24 | 52 | 左电机方向控制2(AIN2) |
| 右电机DIR1 | PA24 | GPIOA.24 | 54 | 右电机方向控制1(BIN1) |
| 右电机DIR2 | PA26 | GPIOA.26 | 59 | 右电机方向控制2(BIN2) |

### 4. 编码器系统
| 功能 | 引脚 | GPIO | 包装引脚 | 中断 | 触发方式 | 说明 |
|------|------|------|----------|------|----------|------|
| 左编码器A相 | PB6 | GPIOB.6 | 23 | GPIOB_INT | 上升沿 | 左轮编码器A相 |
| 左编码器B相 | PB7 | GPIOB.7 | 24 | GPIOB_INT | 下降沿 | 左轮编码器B相 |
| 右编码器A相 | PB8 | GPIOB.8 | 25 | GPIOB_INT | 上升沿 | 右轮编码器A相 |
| 右编码器B相 | PB9 | GPIOB.9 | 26 | GPIOB_INT | 下降沿 | 右轮编码器B相 |

### 5. I2C通信系统

#### 5.1 灰度传感器(I2C0)
| 功能 | 引脚 | GPIO | 包装引脚 | 外设 | 速率 | 说明 |
|------|------|------|----------|------|------|------|
| 灰度SDA | PA0 | GPIOA.0 | 1 | I2C0 | 100kHz | 灰度传感器数据线 |
| 灰度SCL | PA1 | GPIOA.1 | 2 | I2C0 | 100kHz | 灰度传感器时钟线 |

#### 5.2 IMU传感器(I2C1)
| 功能 | 引脚 | GPIO | 包装引脚 | 外设 | 速率 | 说明 |
|------|------|------|----------|------|------|------|
| IMU SDA | PB3 | GPIOB.3 | 16 | I2C1 | 100kHz | IMU传感器数据线 |
| IMU SCL | PB2 | GPIOB.2 | 15 | I2C1 | 100kHz | IMU传感器时钟线 |

### 6. UART通信系统
| 功能 | 引脚 | GPIO | 包装引脚 | 外设 | 波特率 | 说明 |
|------|------|------|----------|------|--------|------|
| UART0_TX | PA10 | GPIOA.10 | 21 | UART0 | 115200 | 调试串口发送 |
| UART0_RX | PA11 | GPIOA.11 | 22 | UART0 | 115200 | 调试串口接收 |
| UART1_TX | PA8 | GPIOA.8 | 19 | UART1 | 115200 | 扩展串口1发送 |
| UART1_RX | PA9 | GPIOA.9 | 20 | UART1 | 115200 | 扩展串口1接收 |
| UART2_TX | PB15 | GPIOB.15 | 32 | UART2 | 115200 | 扩展串口2发送 |
| UART2_RX | PB16 | GPIOB.16 | 33 | UART2 | 115200 | 扩展串口2接收 |
| UART3_TX | PB12 | GPIOB.12 | 29 | UART3 | 115200 | 扩展串口3发送 |
| UART3_RX | PB13 | GPIOB.13 | 30 | UART3 | 115200 | 扩展串口3接收 |

### 7. 用户接口
| 功能 | 引脚 | GPIO | 包装引脚 | 配置 | 说明 |
|------|------|------|----------|------|------|
| LED指示灯 | PB22 | GPIOB.22 | 50 | 输出,下拉 | 状态指示LED |
| 用户按键 | PB21 | GPIOB.21 | 49 | 输入,上拉 | 用户按键输入 |

### 8. 传感器控制
| 功能 | 引脚 | GPIO | 包装引脚 | 配置 | 说明 |
|------|------|------|----------|------|------|
| BNO复位 | PB14 | GPIOB.14 | 31 | 输出 | BNO08x IMU复位控制 |

## 系统配置参数

### 时钟配置
- **外部晶振**: 40MHz HFXT
- **系统时钟**: 80MHz (通过PLL倍频)
- **外设时钟**: 20MHz (PWM定时器)
- **UART时钟**: 4MHz

### 编码器参数
- **编码器类型**: 增量式正交编码器
- **分辨率**: 13线 × 28减速比 × 2倍频 = 728脉冲/转
- **车轮直径**: 6.5cm
- **采样周期**: 10ms

### 电机驱动
- **驱动芯片**: TB6612FNG
- **PWM频率**: 20kHz
- **控制方式**: 双路H桥驱动
- **速度范围**: -100% ~ +100%

### 传感器配置
- **灰度传感器**: 8路数字输出，I2C接口
- **IMU传感器**: BNO08x系列，I2C接口
- **传感器地址**: 灰度传感器(0x4C)

## 软件架构

### 主要模块
1. **motor_app**: 电机控制应用层
2. **gray_app**: 灰度传感器应用层
3. **pid_app**: PID控制应用层
4. **encoder_driver**: 编码器驱动层
5. **motor_driver**: 电机驱动层
6. **uart_driver**: 串口驱动层

### 任务调度
- **调度器**: 基于时间片的任务调度
- **主要任务**:
  - 编码器数据更新 (10ms)
  - PID控制计算 (10ms)
  - 灰度传感器读取 (可配置)
  - 串口通信处理 (事件驱动)

## 使用说明

### 初始化顺序
1. 系统时钟初始化
2. GPIO配置初始化
3. 外设模块初始化
4. 应用层初始化
5. 任务调度器启动

### 调试接口
- **主调试串口**: UART0 (PA10/PA11, 115200bps)
- **调试输出**: 支持printf重定向
- **SWD调试**: 支持在线调试和程序下载

## 注意事项

1. **引脚复用**: 部分引脚具有多种功能，使用时需注意配置
2. **中断优先级**: 编码器中断优先级较高，确保实时性
3. **电源管理**: 注意电机驱动的电源需求
4. **信号完整性**: 高频PWM信号需要良好的PCB布线
5. **调试安全**: 调试时注意电机安全，建议先断开电机电源

## 版本信息
- **硬件版本**: v1.0
- **软件版本**: v1.0
- **开发环境**: Keil MDK-ARM + TI SysConfig
- **SDK版本**: MSPM0 SDK 2.05.01.00
